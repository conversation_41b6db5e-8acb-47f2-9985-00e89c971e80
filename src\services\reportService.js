import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { REPORTS_URLS } from '@/helper/constants/urls';

export const reportService = {
  getRotaReportsList: async (filters) => {
    try {
      const { status, data } = await axiosInstance.get(
        REPORTS_URLS?.GET_ROTA_REPORTS + filters
      );
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  getEmployeeRotaDetails: async (employeeId, filters = '') => {
    try {
      const { status, data } = await axiosInstance.get(
        REPORTS_URLS?.GET_EMPLOYEE_ROTA_DETAILS + `/${employeeId}${filters}`
      );
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  getDocumentsReports: async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS.GET_SUBSCRIPTION_USAGE
      );

      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },
};
