@import '@/styles/variable.scss';

body {
  .documents-reports {
    &__summary {
      margin-bottom: var(--spacing-xl);
      padding: var(--spacing-lg);
      background-color: var(--color-secondary);
      border-radius: var(--border-radius-lg);
      border: var(--normal-sec-border);
      box-shadow: var(--box-shadow-xs);

      &-grid {
        display: flex;
        gap: var(--spacing-md);
        flex-wrap: wrap;
        align-items: center;

        @media (max-width: 768px) {
          flex-direction: column;
          align-items: stretch;
        }
      }

      &-item {
        flex: 1;
        min-width: 150px;

        @media (max-width: 768px) {
          min-width: auto;
          text-align: center;
        }

        &-label {
          font-family: var(--font-family-primary);
          color: var(--text-color-black);
          font-size: var(--font-size-sm);
          line-height: var(--line-height-sm);
          margin-bottom: var(--spacing-xs);
          text-transform: capitalize;
        }

        &-value {
          font-family: var(--font-family-primary);
          color: var(--text-color-primary);
          font-size: var(--font-size-base);
          line-height: var(--line-height-base);
          font-weight: var(--font-weight-semibold);
        }
      }
    }

    &__file-type {
      &-badge {
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-sm);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-semibold);
        text-transform: capitalize;
        display: inline-block;
      }

      &--image {
        background-color: var(--color-success-opacity);
        color: var(--color-success);
      }

      &--video {
        background-color: var(--color-primary-opacity);
        color: var(--color-primary);
      }

      &--pdf {
        background-color: var(--color-danger-opacity);
        color: var(--color-danger);
      }

      &--audio {
        background-color: var(--color-warning-opacity);
        color: var(--color-warning);
      }

      &--default {
        background-color: var(--color-light-success-opacity);
        color: var(--color-light-success);
      }
    }

    &__loading {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: var(--spacing-xxl);
      flex-direction: column;
      gap: var(--spacing-md);

      &-text {
        color: var(--text-color-secondary);
        font-size: var(--font-size-md);
      }
    }

    &__no-data {
      padding: var(--spacing-xxl);
      text-align: center;

      .no-data-wrap {
        height: auto;
        margin-top: 0;
        max-width: 500px;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 1024px) {
    .documents-reports {
      &__container {
        padding: var(--spacing-md);
      }

      &__summary {
        padding: var(--spacing-md);

        &-grid {
          gap: var(--spacing-md);
        }
      }
    }
  }

  @media (max-width: 640px) {
    .documents-reports {
      &__container {
        padding: var(--spacing-sm);
      }

      &__summary {
        padding: var(--spacing-sm);

        &-grid {
          gap: var(--spacing-sm);
        }

        &-item {
          &-value {
            font-size: var(--font-size-md);
          }
        }
      }

      &__table {
        &-title {
          padding: var(--spacing-md) var(--spacing-md) 0;
          font-size: var(--font-size-md);
        }
      }
    }
  }
}
