import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';

export const documentsService = {
  // Get subscription usage data (contains all documents data and summary)
  getSubscriptionUsage: async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS.GET_SUBSCRIPTION_USAGE
      );

      if (status === 200) {
        return {
          success: true,
          data: data.data || data,
        };
      }
      return { success: false, data: null };
    } catch (error) {
      console.error('Error fetching subscription usage:', error);
      throw error;
    }
  },
};
