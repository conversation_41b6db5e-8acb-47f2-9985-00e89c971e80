import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { URLS } from '@/helper/constants/urls';

export const documentsService = {
  // Get documents reports data with filtering, sorting, and pagination
  getDocumentsReports: async (params) => {
    try {
      const queryString = new URLSearchParams(params).toString();
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_DOCUMENTS_REPORTS}?${queryString}`
      );

      if (status === 200) {
        return {
          success: true,
          data: data.data || [],
          totalCount: data.total_count || 0,
          summary: data.summary || null,
        };
      }
      return { success: false, data: [], totalCount: 0 };
    } catch (error) {
      console.error('Error fetching documents reports:', error);
      throw error;
    }
  },

  // Get documents summary/statistics
  getDocumentsSummary: async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS.GET_DOCUMENTS_SUMMARY
      );

      if (status === 200) {
        return {
          success: true,
          data: data,
        };
      }
      return { success: false, data: null };
    } catch (error) {
      console.error('Error fetching documents summary:', error);
      throw error;
    }
  },

  // Export documents report
  exportDocumentsReport: async (params, format = 'xlsx') => {
    try {
      const queryString = new URLSearchParams({
        ...params,
        format,
      }).toString();

      const { status, data } = await axiosInstance.get(
        `${URLS.EXPORT_DOCUMENTS_REPORT}?${queryString}`,
        {
          responseType: 'blob',
        }
      );

      if (status === 200) {
        // Create download link
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement('a');
        link.href = url;

        const filename = `documents_report_${new Date().toISOString().split('T')[0]}.${format}`;
        link.setAttribute('download', filename);

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        return { success: true };
      }
      return { success: false };
    } catch (error) {
      console.error('Error exporting documents report:', error);
      throw error;
    }
  },
};
