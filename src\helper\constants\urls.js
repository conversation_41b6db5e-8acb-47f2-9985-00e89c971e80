export const URLS = {
  // AUTH MODULE
  USER_LOGIN: '/v1/public/auth/login',
  FORGOT_PASSWORD: '/v1/public/auth/forgot-password',
  RESET_PASSWORD: '/v1/public/auth/forgot-password-verify',
  RESET_PASSWORD_LOGIN: '/v1/private/user/reset-password',
  VERIFY_OTP: '/v1/public/auth/verify-otp',
  RESEND_OTP: '/v1/public/auth/resend-otp',
  // DASHBOARD MODULE
  GET_MODEL_LIST: '/v1/private/dashboard/get-dashboard-model',
  GET_MODEL_AXIS_LIST: '/v1/private/dashboard/get-dashboard-model-tab',
  GET_SAVED_DASHBOARD_LIST: '/v1/private/dashboard/get-dashboard-list',
  DELETE_DASHBOARD: '/v1/private/dashboard/delete-dashboard/',
  CREATE_DASHBOARD: '/v1/private/dashboard/create-dashboard',
  UPDATE_DASHBOARD: '/v1/private/dashboard/update-dashboard/',
  GET_DASHBOARD_BY_ID: '/v1/private/dashboard/get-dashboard-by-id/',
  MARK_AS_DEFAULT: '/v1/private/dashboard/mark-dashboard-default/',

  // USER MODULE
  CREATE_USER: '/v1/private/user/create',
  UPDATE_USER: '/v1/private/user/update/',
  DELETE_USER: '/v1/private/user/delete/',
  GET_USER_LIST: '/v1/private/user/list',
  EXPORT_USER_LIST: '/v1/private/user/export-users',
  GET_ROLE_LIST: '/v1/private/user/role-list',
  GET_ONE_USER: '/v1/private/user/get-one-user/',
  RESET_USER_DETAILS: '/v1/private/user/reset-user-profile',
  SEND_INVITATION: '/v1/private/user/send-invitation',
  ORG_SEND_INVITATION: '/v1/private/user/send-reinvitation',
  GET_All_USERNAME: '/v1/public/auth/get-all-username?username=',
  GET_USER_FIELDS: 'v1/private/user/get-user-fields',
  GET_STORED_USER_FIELDS: 'v1/private/user/get-stored-user-field-order',
  STORE_USER_FIELDS_SEQUENCE: '/v1/private/user/store-user-field-sequence',

  GET_INVITATION_USER_LIST: '/v1/private/user/get-invite-user-list',
  UPDATE_USER_CONTRACT: '/v1/private/contract/update-user-contract',
  UPDATE_WORK_SCHEDULE: '/v1/private/user/update-work-schedule',
  DELETE_USER_CONTRACT: '/v1/private/contract/delete-contract-history',

  //MY PROFILE
  MY_PROFILE: '/v1/private/user/view-profile',
  SWITH_ROLE: '/v1/private/user/switch-role',
  UPDATE_MY_PROFILE: '/v1/private/user/update-profile',
  RESET_USER_PASSWORD: '/v1/private/user/reset-user-password',
  GET_COUNTRY_LIST: '/v1/private/user/get-geo-list/',
  GET_SUBSCRIPTION_USAGE: '/v1/private/user/storage-usage',

  // BRANCH MODULE
  CREAT_BRANCH: '/v1/private/branch/add',
  UPDATE_BRANCH: '/v1/private/branch/update/',
  DELETE_BRANCH: '/v1/private/branch/delete/',
  GET_BRANCH_LIST: '/v1/private/branch/list',
  GET_BRANCH_SETTINGS: '/v1/private/setting/get-branch-setting/',
  BRANCH_SETTINGS: '/v1/private/setting/branch-setting',
  GET_ONE_BRANCH: '/v1/private/branch/get-one/',
  HS_CATEGORY_LIST: '/v1/private/setting/get-health-safety-category',
  ADD_TO_CATEGORY: '/v1/private/setting/add-playlist-category',
  GET_BRANCH_HISTORY: '/v1/private/setting/get-branch-activity/',
  GET_BRANCH_CAT_LIST: '/v1/private/category/get-branch-category',
  ADD_BRANCH_CAT: '/v1/private/category/add-category-branch',
  GET_CATEGORY_BRANCH_LIST: '/v1/private/category/get-branch-list-category/',
  ADD_MULTIPLE_CATEGORY_BRANCH:
    '/v1/private/category/add-multiple-category-branch',

  // DEPARTMENT MODULE
  CREATE_DEPARTMENT: '/v1/private/department/add',
  UPDATE_DEPARTMENT: '/v1/private/department/update/',
  DELETE_DEPARTMENT: '/v1/private/department/delete/',
  GET_DEPARTMENT_LIST: '/v1/private/department/list',

  // ONBOARDING
  GET_ONBOARDING_CHECKLIST: '/v1/private/onbording/get-check-list',
  GET_HEALTH_SAFETY: '/v1/private/onbording/get-health-safety-checklist',
  GET_HEALTH_SAFETY_PROGRESS:
    '/v1/private/onbording/get-health-safety-progress',
  GET_ONBOARDING_FORM_DETAILS: '/v1/private/onbording/get-form-detail',
  UPDATE_ONBOARDING_FORM: '/v1/private/onbording/update-form',
  CREATE_ONBOARDING_FORM: '/v1/private/onbording/create-form',
  DELETE_UPLOADED_FILE: '/v1/private/onbording/delete-file',
  APPROVE_REJECT_ONBOARDING: '/v1/private/onbording/approve-reject-form',
  GET_JOINING_REMOVING_CHECKLIST:
    '/v1/private/onbording/get-verification-checklist',
  CHECKLIST_VERIFICATION: '/v1/private/onbording/checklist-verification',
  REQUEST_TO_ADMIN: '/v1/private/onbording/request-form',
  GET_RIGHT_TO_WORK_FORM: '/v1/private/onbording/get-right-to-work-form',
  DOWNLOAD_ZIP: '/v1/private/onbording/download-right-to-work-zip',
  USER_VERIFICATION: '/v1/private/userVerification/user-verification',
  USER_ONBOARDING_RESET: '/v1/private/onbording/user-onboarding-reset',
  ONBOARDING_DELETE_FILE: '/v1/private/onbording/delete-file',
  // LEAVE
  GET_LEAVE_REMARK: '/v1/private/request/get-staff-leave-list',
  LEAVE_ACTION: '/v1/private/request/approve-reject-request',
  GET_OWN_LEAVE: '/v1/private/request/get-leave-list',
  APPLY_LEAVE: '/v1/private/request/apply-leave',
  USER_LEAVE_POLICIES: '/v1/private/request/get-user-leave-policies/',
  USER_LEAVE_POLICIES_BALANCE: '/v1/private/request/get-user-leave-balance/',
  CALENDER_WISE_LEAVE: '/v1/private/request/get-calender-wise-leave',
  GET_LEAVE_BALANCE: '/v1/private/request/get-leave-reports',
  GET_LEAVE_CONSUMPTION_REPORTS:
    '/v1/private/request/get-leave-consumption-reports',
  GET_LEAVE_TYPE_REPORTS: '/v1/private/request/get-leave-type-reports',
  GET_ONE_USER: '/v1/private/user/get-one-user/',
  GET_SINGLE_USER: '/v1/private/request/get-leave-by-id',
  DELETE_LEAVE: '/v1/private/request/cancel-leave-request',

  // RESIGNATION
  GET_RESIGNATION_DETAILS: '/v1/private/resignation/get-resignation',
  SEND_RESIGNATION: '/v1/private/resignation/send-resignation ',
  GET_RESIGNATION_LIST: '/v1/private/resignation/get-resignation-list',
  UPDATE_RESIGNATION: '/v1/private/resignation/update-resignation-request/',
  GET_ONE_USER_RESIGNATION: '/v1/private/resignation/get-resignation',
  GET_LEAVING_CHECKLIST: '/v1/private/resignation/get-Leaving-checklist/',
  VERIFY_LEAVING_CHECKLIST: '/v1/private/resignation/verify-Leaving-checklist/',

  // NOTIFICATION
  GET_STAFF_NOTIFICATON: '/v1/private/notification/get-staff-notification',
  GET_OWN_NOTIFICATON: '/v1/private/notification/get-own-notification',
  SEND_NOTIFICATION: '/v1/private/notification/send-notification',
  MARK_AS_READ_NOTIFICATION: '/v1/private/notification/mark-as-read',

  // GENERAL SETTINGS
  GENERAL_SETTINGS: '/v1/private/setting/add',
  GET_GENERAL_SETTINGS: '/v1/private/setting/list',

  // ACTIVITY LOGS
  ACTIVITY_LOGS: '/v1/private/user/get-activity-log',
  USER_ACTIVITY: '/v1/private/user/get-activity-by-user',

  // PERMISSION MODULE
  USER_PERMISSION: '/v1/private/user/get-permission-list',
  UPDATE_ONESIGNAL_TOKEN: '/v1/private/user/update-notification-token',
  LOGOUT: '/v1/private/user/logout',

  // DSR SETTINGS
  CREATE_DSR_PAYMENT: '/v1/private/dsr/create-payment-type',
  UPDATE_DSR_PAYMENT: '/v1/private/dsr/update-payment-type/',
  DELETE_DSR_PAYMENT: '/v1/private/dsr/delete-payment-type/',
  PAYMENT_LIST_REORDER: '/v1/private/dsr/update-payment-type-order',
  GET_PAYMENT_ALL_LIST: '/v1/private/dsr/get-all-payment-type-child',
  GET_PAYMENT_BRANCH_ALL_LIST:
    '/v1/private/dsr/get-all-payment-type-child-branch/',
  CREATE_DSR_SUB_PAYMENT: '/v1/private/dsr/create-payment-type-category/',
  UPDATE_DSR_SUB_PAYMENT: '/v1/private/dsr/update-payment-type-category/',
  DELETE_DSR_SUB_PAYMENT: '/v1/private/dsr/delete-payment-type-category/',
  ADD_SUB_PAYMENT: '/v1/private/dsr/add-field-value/',
  UPDATE_SUB_PAYMENT: '/v1/private/dsr/update-field-value',
  GET_FIELD_SUB: '/v1/private/dsr/get-field-value-by-id/',
  DELETE_SUB_PAYMENT: '/v1/private/dsr/delete-category-branch-value/',
  PAYMENT_VALUE_ORDER: '/v1/private/dsr/update-category-value-order',
  PAYMENT_DEFAULT_ACTIVE: '/v1/private/dsr/make-category-default-active',
  PAYMENT_SUB_LIST_REORDER:
    '/v1/private/dsr/update-payment-type-category-order',

  // DSR
  GET_DSR_DATA: '/v1/private/dsr/get-payment-type/',
  ADD_DSR: '/v1/private/dsr/add',
  UPDATE_DSR: '/v1/private/dsr/update/',
  ADD_WSR: '/v1/private/dsr/add-wsr',
  UPDATE_WSR: '/v1/private/dsr/update-wsr/',
  ADD_EXPENSE: '/v1/private/expense/add',
  UPDATE_EXPENSE: '/v1/private/expense/update/',

  //DSR AND DSR REQUEST
  GET_DSR_LIST: '/v1/private/dsr/get-dsr-list',
  GET_DSR_REQUEST_LIST: '/v1/private/dsr/get-dsr-request-list',
  GET_WSR_REQUEST_LIST: '/v1/private/dsr/get-wsr-request-list',
  GET_EXPENSE_REQUEST_LIST: '/v1/private/expense/get-expense-request-list',
  GET_ACTIVITY_LOG: '/v1/private/dsr/get-dsr-activity/',
  GET_WSR_ACTIVITY_LOG: '/v1/private/dsr/get-wsr-activity/',
  GET_EXPENSES_ACTIVITY_LOG: '/v1/private/expense/get-expense-activity/',
  GET_PAYMENT_TYPE: '/v1/private/dsr/get-payment-type/',
  GET_REQUEST_BY_ID: '/v1/private/dsr/get-dsr-request-by-id/',
  GET_REQUEST_WSR_ID: '/v1/private/dsr/get-wsr-request-by-id/',
  GET_REQUEST_EXPENSE_ID: '/v1/private/expense/get-expense-request-by-id/',
  GET_DSR_BY_ID: '/v1/private/dsr/get-dsr-by-id/',
  GET_DSR_CHECK: '/v1/private/dsr/check-dsr-exist',
  APPROVE_REJECT_REQUEST: '/v1/private/dsr/approve-reject-request',
  APPROVE_REJECT_REQUEST_WSR: '/v1/private/dsr/approve-reject-wsr-request',
  APPROVE_REJECT_REQUEST_EXPENSE: '/v1/private/expense/approve-reject-request',
  GET_DSR_REPORT: '/v1/private/dsr/get-dsr-report',
  DOWNLOAD_REPORT: '/v1/private/dsr/download-pdf-excel',
  GET_WSR_LIST: '/v1/private/dsr/get-wsr-list',
  GET_EXPENSE_LIST: '/v1/private/expense/get-expense-list',
  GET_WSR_CHECK: '/v1/private/dsr/check-wsr-exist',
  GET_EXPENSE_CHECK: '/v1/private/expense/check-expense-exist',
  GET_WSR_BY_ID: '/v1/private/dsr/get-wsr-by-id/',
  GET_EXPENSE_BY_ID: '/v1/private/expense/get-expense-by-id/',

  // DSR REPORTS
  DATE_FILTER_LIST: '/v1/private/report/filter-list',
  REPORT_FILTER_BY_USER_ID: '/v1/private/report/get-user-filter-list',
  GET_REPORT_BY_ID: '/v1/private/report/get-user-filter-by-id',
  GET_FILTER_CATEGORY_LIST: '/v1/private/report/filter-category-list',
  GET_DSR_REPORT_FILTER: '/v1/private/dsr/get-dsr-report',
  SAVE_REPORTS: '/v1/private/report/save-filter',
  UPDATE_REPORTS: '/v1/private/report/update-filter/',
  REMOVE_FILTER: '/v1/private/report/remove-user-filter/',
  EXPORT_REPORT: '/v1/private/dsr/download-pdf-excel',
  // CHANGE REQUEST
  ADD_CR: '/v1/private/changeRequest/send-change-request',
  GET_CR_LIST: '/v1/private/changeRequest/get-change-request-list',
  GET_OWN_CR_LIST: '/v1/private/changeRequest/get-own-change-request-list',
  GET_CR_BY_ID: '/v1/private/changeRequest/get-change-request-by-id/',
  APPROVE_REJECT_CR: '/v1/private/changeRequest/approve-reject-change-request/',
  REGENERATE_EMP_CONTRACT:
    '/v1/private/onbording/regenerate-employment-contract/',
  DELETE_CHANGE_REQUEST:
    '/v1/private/changeRequest/delete-change-request-by-id/',
  GET_CR_FIELDS: '/v1/private/changeRequest/get-change-request-fields',
  STORE_CR_FIELDS: '/v1/private/changeRequest/store-change-request-fields',
  GET_STORED_CR_FIELDS:
    '/v1/private/changeRequest/get-stored-change-request-field',

  // EMP CONTRACT
  CREATE_EMP_CONTRACT: '/v1/private/contract/create',
  GET_FOLDER_LIST: '/v1/private/contract/category',
  GET_FILE_LIST: '/v1/private/contract/templates',
  GET_TEMPLATE_VERSION: '/v1/private/contract/template/versions',
  GET_RENEWL_LIST: '/v1/private/contract/forms/versions',
  DELETE_TEMPLATES: '/v1/private/contract/templates',
  REGENERATE_CONTRACT_FORM: '/v1/private/contract/forms',
  COPY_CONTRACT: '/v1/private/contract/copy',
  MOVE_CONTRACT: '/v1/private/contract/move',
  GET_USER_CONTRACT: '/v1/private/contract/get-users-by-template-group',

  // LEAVE POLICY
  GET_LEAVE_TYPE: '/v1/private/request/leave-types',
  LEAVE_TYPE: '/v1/private/request/leave-type',
  GET_LEAVE_POLICY: '/v1/private/request/leave-policies',
  LEAVE_POLICY: '/v1/private/request/leave-policy',
  GET_CONTRACT_TYPE: '/v1/private/contract/contract-types',
  GET_USER_CONTRACT_TYPE: '/v1/private/contract/get-contract-user-type',
  ADD_USER_CONTRACT_TYPE: '/v1/private/contract/contract-user-type',
  CONTRACT_TYPE: '/v1/private/contract/contract-type',
  ADD_LEAVE_POLICY: '/v1/private/leavePolicy/add-leave-policy',
  UPDATE_LEAVE_POLICY: '/v1/private/leavePolicy/update-leave-policy/',
  GET_LEAVE_POLICY_BY_ID: '/v1/private/leavePolicy/get-leave-policy-by-id/',
  REMOVE_POLICY: '/v1/private/leavePolicy/remove-leave-policy/',
  LEAVE_BALANCE: '/v1/private/request/get-user-leave-balance/',
  GET_LEAVE_SETTING: '/v1/private/policySetting/get-policy-setting',
  LEAVE_SETTING: '/v1/private/policySetting/add-policy-setting',

  //HOLIDAY
  GET_HOLIDAY_LIST: '/v1/private/holiday/get-holiday-type-list',
  CREATE_HOLIDAY_TYPE: '/v1/private/holiday/create-holiday-type',
  UPDATE_HOLIDAY_TYPE: '/v1/private/holiday/update-holiday-type/',
  CREATE_HOLIDAY: '/v1/private/holiday/add-holiday-policy/',
  UPDATE_HOLIDAY: '/v1/private/holiday/update-holiday-policy/',
  GET_hOLIDAY_POLICY_BY_ID: '/v1/private/holiday/get-holiday-policy/',
  DELETE_HOLIDAY_POLICY: '/v1/private/holiday/delete-holiday-policy/',
  DELETE_HOLIDAY_TYPE: '/v1/private/holiday/delete-holiday-type/',
  INACTIVE_ACTIVE_HOLIDAY: '/v1/private/holiday/change-holiday-type-status',
  ASSIGN_EMPLOYEE: '/v1/private/user/assign-policy',
  IMPORT_HOLIDAY: '/v1/private/holiday/import-holiday-policy/',
  GET_USER_HOLIDAY_POLICY: '/v1/private/holiday/get-user-holiday-policy',

  // DOCUMENT CENTER
  CREATE_CAT_DOCUMENT: '/v1/private/category/add-category',
  UPDATE_CAT_DOCUMENT: '/v1/private/category/update-category/',
  GET_DOCUMENT_STAFF_LIST: '/v1/private/category/get-category-list',
  GET_DOCUMENT_FOLDER_STAFF_LIST: '/v1/private/category/get-all-category-list',
  GET_DOCUMENT_OWN_LIST: '/v1/private/category/get-own-category-list',
  GET_DOCUMENT_FOLDER_OWN_LIST:
    '/v1/private/category/get-all-own-category-list',
  DELETE_DOCUMENT: '/v1/private/category/delete-category',
  GET_ONE_CATEGORY: '/v1/private/category/get-category/',
  UPDATE_CAT_ITEM_ORDER: '/v1/private/category/update-category-item-order',
  MOVE_CATEGORY: '/v1/private/category/move-category',
  COPY_CATEGORY: '/v1/private/category/copy-category',
  GET_STATISTICS_USER_LIST: '/v1/private/category/get-user-statistics-list',
  USER_MEDIA_TRACK: '/v1/private/category/track-category',
  USER_RESET_CATEGORY_TRACK: '/v1/private/category/restore-track-category',
  USER_RESET_ALL_CATEGORY_TRACK:
    '/v1/private/category/all-restore-track-category',
  USER_TRACK_LOGS: '/v1/private/category/get-category-track-activity',

  // DOCUMENTS REPORTS
  GET_DOCUMENTS_REPORTS: '/v1/private/documents/reports',
  GET_DOCUMENTS_SUMMARY: '/v1/private/documents/summary',
  EXPORT_DOCUMENTS_REPORT: '/v1/private/documents/reports/export',

  //  BUDGET AND FORECAST
  ADD_FORECAST: '/v1/private/forecast/add',
  UPDATE_FORCAST: '/v1/private/forecast/update-forecast/',
  ASSIGN_FORCAST: '/v1/private/forecast/assign-budget',
  SAVE_FORCAST: '/v1/private/forecast/add-forecast-bugdet',
  DELETE_FORCAST: '/v1/private/forecast/delete-revoke-budget/',
  LOCK_FORCAST: '/v1/private/forecast/lock-forecast/',
  EXPORT_FORCAST: '/v1/private/forecast/download-forecast-report',
  GET_FORECAST_CAT_LIST: '/v1/private/forecast/get-forecast-category',
  GET_FORECAST_LIST: '/v1/private/forecast/get-forecast-list',
  GET_DELETE_FORECAST_LIST: 'v1/private/forecast/delete-budget-history',
  GET_BUDGET_CHART_LIST: '/v1/private/forecast/get-forecast-chart-list',
  GET_FORECAST_BY_ID: '/v1/private/forecast/get-single-forecast/',
  GET_FORECAST_Details_BY_ID:
    '/v1/private/forecast/get-forecast-budget-details/',
  GET_BUDGET_HISTORY: '/v1/private/forecast/get-forecast-history-list',
  GET_BUDGET_HISTORY_BY_ID:
    '/v1/private/forecast/get-forecast-history-details/',
  //Side letter
  ADD_SIDE_LETTER: '/v1/private/sideLetter/add-side-letter',
  GET_SLIDER_LIST: '/v1/private/sideLetter/get-side-letter-list',
  DELETE_SLIDE_LETTER: '/v1/private/sideLetter/delete-side-letter',
  UPDATE_SLIDE_STATUS: '/v1/private/sideLetter/update-status/',
};
export const ORG_URLS = {
  // COUNTRY CODE
  GET_COUNTRY: '/auth-api/v1/public/auth/country-list',
  GET_CURRENCIES: '/auth-api/v1/public/auth/currency-list',
  GET_TIMEZONE: '/auth-api/v1/public/auth/timezone-list',

  // AUTH MODULE
  USER_LOGIN: '/auth-api/v1/public/auth/login',
  FORGOT_PASSWORD: '/auth-api/v1/public/auth/forgot-password',
  RESET_PASSWORD: '/auth-api/v1/public/auth/forgot-password-verify',
  RESET_PASSWORD_LOGIN: '/auth-api/v1/private/user/reset-password',
  VERIFY_OTP: '/auth-api/v1/public/auth/verify-otp',
  RESEND_OTP: '/auth-api/v1/public/auth/resend-otp',
  ORG_REGISTER: '/auth-api/v1/public/auth/org-user-register',
  FREE_DEMO: '/auth-api/v1/public/demo-request/add-demo-request',

  // ORGANIZATION MODULE
  VERIFY_AUTH_TOKEN: '/auth-api/v1/public/auth/verify-auth-token',
  RESED_AUTH_OTP: '/auth-api/v1/public/auth/resend-otp',
  RESED_AUTH_EMAIL: '/auth-api/v1/public/auth/resend-email',
  VERIFY_AUTH_OTP: '/auth-api/v1/public/auth/verify-otp',
  RESET_AUTH_PASSWORD: '/auth-api/v1/public/auth/reset-password',
  CHANGE_USER_PASSWORD: '/auth-api/v1/private/auth/change-password',
  GET_ORGANIZATION_BY_ID: '/auth-api/v1/private/organization/get-org/',
  GET_USER_DATA_BY_USER_ID: '/auth-api/v1/public/auth/get-user-data',
  UPDATE_ORG_DATA: '/auth-api/v1/private/organization/update-organization',
  UPDATE_USER_DATA: '/auth-api/v1/private/auth/update-user',
  CLOSE_ACCONT: '/auth-api/v1/private/organization/close-organization-account/',

  // PAYMENT MODULE
  CREATE_CARD: '/subscription-api/v1/private/card/create-card',
  UPDATE_CARD: '/subscription-api/v1/private/card/update-card',
  CREATE_NEW_PAYMENT_PROVIDER:
    '/subscription-api/v1/private/payment/create-payment-provider',
  GET_ALL_PAYMENT_PROVIDER:
    '/subscription-api/v1/private/payment/get-payment-providers',
  GET_ALL_PUBLIC_PAYMENT_PROVIDER:
    '/subscription-api/v1/public/payment/get-payment-providers',
  GET_PAYMENT_PROVIDER_BY_ID:
    '/subscription-api/v1/private/payment/get-payment-provider',
  UPDATE_PAYMENT_PROVIDER:
    '/subscription-api/v1/private/payment/update-payment-provider',
  CREATE_SUBSCRIPTION_PLAN:
    '/subscription-api/v1/private/subscription/create-plan',
  GET_ALL_SUBSCRIPTION_PLAN:
    '/subscription-api/v1/private/subscription/get-plans',
  GET_SUBSCRIPTION_PLAN_BY_ID:
    '/subscription-api/v1/private/subscription/get-plan',
  UPDATE_SUBSCRIPTION_PLAN:
    '/subscription-api/v1/private/subscription/update-plan',
  GET_ALL_CARDS: '/subscription-api/v1/private/card/get-cards',
  GET_ALL_PURCHASED_SUBSCRIPTION_PLAN:
    '/subscription-api/v1/private/subscription/get-purchased-plan',
  GET_ALL_ORGANIZATION_DATA:
    '/subscription-api/v1/private/admin/get-org-subscription-list',
  PURCHASE_PLAN: '/subscription-api/v1/private/subscription/purchased-plan',
  UPDATE_PLAN: '/subscription-api/v1/private/subscription/upgrade-plan',
  GET_PLAN_BY_ID: '/subscription-api/v1/private/subscription/get-plan/',
  GET_ALL_PAYMENT_HISTORY:
    '/subscription-api/v1/private/subscription/get-all-payment-history',
  GENERATE_INVOICE: '/subscription-api/v1/private/payment/generate-invoice-pdf',
  GET_ORGANIZATION_BY_ID_PLAN:
    '/subscription-api/v1/private/admin/get-org-plan/',
  PUBLIC_LINK_PAYMENT:
    '/subscription-api/v1/public/subscription/public-link-payment',
  UPDATE_SUB_PLAN: '/subscription-api/v1/private/admin/update-org-subscription',
  CANCEL_SUB_PLAN: '/subscription-api/v1/private/subscription/cancel-plan',
  GET_INVOICE_PREVIEW: '/subscription-api/v1/private/payment/get-invoice-data',
  GET_USER_BANNER_NOTIFICATION:
    '/backend-api/v1/private/banner/get-banner-notifications',
  MARK_AS_READ_BANNER_NOTIFICATION:
    '/backend-api/v1/private/banner/mark-as-read',

  // NOTIFICATION
  GET_NOTIFICATION_LIST:
    '/backend-api/v1/private/notification/get-all-notification',
};

export const RECIPE_URLS = {
  GET_ALL_CATEGORIES: '/recipe-api/v1/private/category/list',
  CREATE_CATEGORIES: '/recipe-api/v1/private/category/create',
  UPDATE_CATEGORY: '/recipe-api/v1/private/category/update',
  DELETE_CATEGORY: '/recipe-api/v1/private/category/delete',
  GET_ALL_ALLERGENS: '/recipe-api/v1/private/food-attributes/list',
  CREATE_ALLERGEN: '/recipe-api/v1/private/food-attributes/create',
  UPDATE_ALLERGEN: '/recipe-api/v1/private/food-attributes/update',
  DELETE_ALLERGEN: '/recipe-api/v1/private/food-attributes/delete',
  GET_ALL_INGREDIENTS: '/recipe-api/v1/private/ingredients/get-all',
  GET_INGREDIENT_BY_ID: '/recipe-api/v1/private/ingredients/get-by-id',
  CREATE_INGREDIENT: '/recipe-api/v1/private/ingredients/create',
  UPDATE_INGREDIENT: '/recipe-api/v1/private/ingredients/update',
  DELETE_INGREDIENT: '/recipe-api/v1/private/ingredients/delete',
  EXPORT_INGREDIENT: '/recipe-api/v1/private/ingredients/export',
  IMPORT_INGREDIENT: '/recipe-api/v1/private/ingredients/import',
  RECIPE_MEASURES: '/recipe-api/v1/private/recipe-measures/list',
  GET_ALL_RECIPES: '/recipe-api/v1/private/recipes/list',
  UPDATE_BOOKMARK: '/recipe-api/v1/private/recipes/bookmark',
  DUPLICATE_RECIPE: '/recipe-api/v1/private/recipes/duplicate',
  DELETE_RECIPE: '/recipe-api/v1/private/recipes/delete',
  RECIPES_SETTINGS: '/recipe-api/v1/private/settings/recipe-configuration',
  UPDATE_RECIPES_SETTINGS:
    '/recipe-api/v1/private/settings/update-recipe-configuration',
  RECIPE_PREVIEW: '/recipe-api/v1/private/recipes/get-by-id',
  CREATE_RECIPES: '/recipe-api/v1/private/recipes/create',
  UPDATE_RECIPES: '/recipe-api/v1/private/recipes/update',
  GET_ATTRIBUTES: '/recipe-api/v1/private/food-attributes/list',
  GET_PUBLIC_ATTRIBUTES: '/recipe-api/v1/public/food-attributes/list',
  GET_PRIVATE_RECIPE_LIST: '/recipe-api/v1/private/analytics/track/recipe-view',
  GET_PUBLIC_RECIPE_LIST: '/recipe-api/v1/public/recipes/list',
  GET_PUBLIC_RECIPE_PREVIEW: '/recipe-api/v1/public/recipes/get-by-id',
  GET_PUBLIC_INGREDIENTS: '/recipe-api/v1/public/ingredients/get-all',
  GET_PUBLIC_ALLERGENS: '/recipe-api/v1/public/food-attributes/list',
  GET_PUBLIC_CATEGORIES: '/recipe-api/v1/public/category/list',
  GET_RECIPE_HISTORY: '/recipe-api/v1/private/recipes/history',

  GET_PUBLIC_ANALYTICS: '/recipe-api/v1/private/dashboard/public-analytics',
  GET_PUBLIC_ANALYTICS_OVERVIEW: '/recipe-api/v1/private/dashboard/overview',
  GET_PUBLIC_CTA_ANALYTICS: '/recipe-api/v1/private/analytics/cta-analytics',
  GET_PUBLIC_CONTACT_ANALYTICS:
    '/recipe-api/v1/private/analytics/contact-analytics',
  DELETE_PUBLIC_CONTACT_ANALYTICS:
    '/recipe-api/v1/private/analytics/contact-analytics',

  EXPORT_PUBLIC_CONTACT_ANALYTICS:
    '/recipe-api/v1/private/analytics/contact-analytics/export',
  EXPORT_CTA_ANALYTICS: '/recipe-api/v1/private/analytics/cta-analytics/export',
  GET_RECIPE_VIEW_STATISTICS:
    '/recipe-api/v1/private/analytics/recipe-view-statistics',
  RESET_RECIPE_VIEW_STATISTICS:
    '/recipe-api/v1/private/analytics/reset-view-statistics',

  ASSIGN_RECIPE: '/recipe-api/v1/private/recipes/manage-assignments',
  EXPORT_RECIPE: '/recipe-api/v1/private/recipes/export',
  EXPORT_RECIPE_DASHBOARD: '/recipe-api/v1/private/dashboard/export',
  TRACK_RECIPE_VIEW: '/recipe-api/v1/public/analytics/track/recipe-view',
  TRACK_CTA_CLICKS: '/recipe-api/v1/public/analytics/track/cta-click',
  SUBMIT_CONTACT_FORM: '/recipe-api/v1/public/analytics/contact-form',
  RECIPE_BASIC_INFO: '/recipe-api/v1/private/recipes/basic-info',
  INGREDIENTS_NUTRITION: '/recipe-api/v1/private/recipes/ingredients-nutrition',
  UPLOAD_RECIPE_FILES: '/recipe-api/v1/private/recipes/upload-recipe-files',
  DELETE_RECIPE_FILES: '/recipe-api/v1/private/recipes/delete-recipe-resources',
  ADD_RECIPE_FILES: '/recipe-api/v1/private/recipes/add-recipe-resources',
  RECIPE_STEPS: '/recipe-api/v1/private/recipes/recipe-steps',
  ORGANIZATION_SLUGS: '/recipe-api/v1/private/settings/organization-slugs',
};

export const SUPPORT_TICKET_URLS = {
  SUPPORT_TICKET_DASHBOARD: '/support-api/v1/private/admin/dashboard',
  GET_ALL_TICKET_LIST: '/support-api/v1/private/tickets/list',
  GET_SINGLE_TICKET_DETAILS: '/support-api/v1/private/tickets',
  GET_ORG_USER_LIST: '/support-api/v1/private/admin/org-users',
  CREATE_TICKET: '/support-api/v1/private/tickets/create',
  UPDATE_TICKET: '/support-api/v1/private/tickets/update',
  DELETE_TICKET: '/support-api/v1/private/tickets/delete',
  GET_TICKET_CONVERSATION: '/support-api/v1/private/messages/ticket',
  ADD_TICKET_MESSAGE: '/support-api/v1/private/messages/ticket',
  TICKET_HISTORY: '/support-api/v1/private/tickets/history',
};

export const ROTA_URLS = {
  SHIFT_URL: '/rota-api/v1/private/shifts',
  SHIFT_STAFF_LIST: '/backend-api/v1/private/user/list',
  SHIFT_CHECK_AVILABILITY: '/rota-api/v1/private/shifts/check-availability',
  SHIFT_PUBLISHE_UNPUBLISH: '/rota-api/v1/private/shifts/publish-unpublish',
  SET_AS_OPEN_SHIFT: '/rota-api/v1/private/shifts/set-as-open-shift',
  DAY_OFF_URL: '/rota-api/v1/private/day-offs',
  SHIFT_HISTORY_URL: '/rota-api/v1/private/shifts/history',
  SWAP_SHIFT_URL: '/rota-api/v1/private/shifts/swap-shift',
  SWAP_ACTION_URL: '/rota-api/v1/private/shifts/swap-action',
  DROP_SHIFT_URL: '/rota-api/v1/private/shifts/drop-shift',
  DROP_ACTION_URL: '/rota-api/v1/private/shifts/drop-action',
  AVAILABILITY_URL: '/rota-api/v1/private/availability',
  COPY_SHIFT_RANGE: '/rota-api/v1/private/shifts/copy-shifts-range',
  CLEAR_SHIFT: '/rota-api/v1/private/shifts/clear-week-shifts',
  EXPORT_SHIFT_EXCEL: '/rota-api/v1/private/shifts/export-shifts-to-excel',
  UPDATE_USER_ORDER: '/rota-api/v1/private/shifts/update-user-order',
  SHIFTS_CHANGE_GROUP_BY: '/rota-api/v1/private/shifts/change-group-by',
};
export const REPORTS_URLS = {
  GET_ROTA_REPORTS: '/rota-api/v1/private/shifts/rota-reports',
  GET_EMPLOYEE_ROTA_DETAILS:
    '/rota-api/v1/private/shifts/employee-rota-details',
};
export const INFO_LINKS = {
  contactUs: 'https://microffice.co.uk/contact/',
  home: 'https://microffice.co.uk/',
  TermsConditions: 'https://microffice.co.uk/term-conditions/',
  PrivacyPolicy: 'https://microffice.co.uk/privacy-policy/',
};
export const CONTACT_NO = {
  phone: '+44 7485129630',
};
