'use client';
import React, { useEffect, useState, useContext } from 'react';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import { Box } from '@mui/material';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import Icon from '@/components/UI/AppIcon/AppIcon';
import { reportService } from '@/services/reportService';
import { ROTA_URLS } from '@/helper/constants/urls';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import AuthContext from '@/helper/authcontext';
import moment from 'moment';
import RotaReportDetails from './RotaReportDetails';
import { staticOptions } from '@/helper/common/staticOptions';

const columns = [
  { header: 'Employee ID', accessor: 'employment_number', sortable: true },
  { header: 'User Name', accessor: 'user_full_name', sortable: false },
  { header: 'Branch / Department', accessor: 'branch', sortable: false },
  { header: 'Total Shifts', accessor: 'total_shifts', sortable: true },
  { header: 'Total Hours', accessor: 'total_hours', sortable: true },
  { header: 'Total Breaks', accessor: 'total_break_hours', sortable: true },
];

export default function RotaReports() {
  const { AllListsData } = useContext(AuthContext);
  const [filters, setFilters] = useState({ date_period: 'month' });
  const [filteredData, setFilteredData] = useState([]);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Details view state
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  // Dynamic filter options state
  const [branchOptions, setBranchOptions] = useState([
    { label: 'Select Branch', value: '' },
    ...AllListsData.ActiveBranchList,
  ]);
  const [departmentOptions, setDepartmentOptions] = useState([
    { label: 'Select Department', value: '' },
  ]);
  const [userOptions, setUserOptions] = useState([
    { label: 'Select User', value: '' },
  ]);
  const [loading, setLoading] = useState(false);

  var weekStart = moment(new Date())
    .clone()
    .startOf('isoWeek')
    .format('YYYY-MM-DD');
  var weekEnd = moment(new Date())
    .clone()
    .endOf('isoWeek')
    .format('YYYY-MM-DD');

  var firstDay = moment(new Date()).startOf('month').format('YYYY-MM-DD');
  var lastDay = moment(new Date()).endOf('month').format('YYYY-MM-DD');

  useEffect(() => {
    // Use AllListsData for branches if available
    if (AllListsData?.ActiveBranchList) {
      setBranchOptions([
        { label: 'Select Branch', value: '' },
        ...AllListsData.ActiveBranchList,
      ]);
    }
  }, [AllListsData.ActiveBranchList]);

  useEffect(() => {
    setDepartmentOptions([
      { label: 'Select Department', value: '' },
      ...AllListsData?.ActiveDepartmentList,
    ]);
  }, [AllListsData?.ActiveDepartmentList]);

  // Fetch users based on selected branch
  const fetchUsers = async (branchId = '', department = '') => {
    try {
      const { status, data } = await axiosInstance.get(
        ROTA_URLS?.SHIFT_STAFF_LIST +
          `?isAdmin=false&branch_id=${branchId}&department_id=${department}&isRotaList=true`
      );

      if (status === 200) {
        const userList =
          data?.userList?.map((user) => ({
            label: user?.user_full_name,
            value: user?.id,
          })) || [];
        setUserOptions([{ label: 'Select User', value: '' }, ...userList]);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      setUserOptions([{ label: 'Select User', value: '' }]);
    }
  };

  const fetchRotaReportsList = async (
    filterParams = '',
    page = currentPage,
    size = rowsPerPage
  ) => {
    setLoading(true);
    try {
      // If no filter params provided, use current month as default
      let params = filterParams;
      if (!params) {
        if (filters.date_period === 'week') {
          params = `?from=${weekStart}&to=${weekEnd}`;
        } else if (filters.date_period === 'month') {
          params = `?from=${firstDay}&to=${lastDay}`;
        }
      }

      // Add pagination parameters
      const separator = params.includes('?') ? '&' : '?';
      const paginationParams = `${separator}page=${page}&size=${size}`;
      const finalParams = params + paginationParams;

      const data = await reportService.getRotaReportsList(finalParams);
      setFilteredData(data?.data || []);
      setTotalCount(data?.count || 0);
    } catch (error) {
      console.error('Error fetching rota reports:', error);
      setFilteredData([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchUsers('', '');
    fetchRotaReportsList();
  }, []);

  // Dynamically build filter fields
  const filterFields = [
    {
      type: 'search',
      label: 'Search',
      name: 'search',
      placeholder: 'Enter Search',
    },
    {
      type: 'select',
      label: 'Branch',
      name: 'branch',
      options: branchOptions,
      placeholder: 'Select Branch',
    },
    {
      type: 'select',
      label: 'Department',
      name: 'department',
      options: departmentOptions,
      placeholder: 'Select Department',
    },
    {
      type: 'select',
      label: 'User',
      name: 'user',
      options: userOptions,
      placeholder: 'Select User',
    },

    {
      type: 'select',
      label: 'Date',
      name: 'date_period',
      options: staticOptions?.PERIOD_OPTIONS,
      placeholder: 'Select Date',
    },
  ];

  // Insert date-range field if date_period is 'custom'
  if (filters.date_period === 'custom') {
    filterFields.push({
      type: 'date-range',
      label: 'Date Range',
      name: 'dateRange',
      placeholder: 'Select date range',
      format: 'MMM dd, yyyy',
    });
  }

  // Build API query parameters from filters
  const buildFilterParams = (
    filterValues,
    page = currentPage,
    size = rowsPerPage
  ) => {
    const params = new URLSearchParams();

    // Handle date range
    let startDate, endDate;
    if (filterValues.date_period === 'week') {
      startDate = weekStart;
      endDate = weekEnd;
    } else if (filterValues.date_period === 'month') {
      startDate = firstDay;
      endDate = lastDay;
    } else if (
      filterValues.date_period === 'custom' &&
      filterValues.dateRange &&
      filterValues.dateRange.length === 2
    ) {
      startDate = new Date(filterValues.dateRange[0])
        .toISOString()
        .split('T')[0];
      endDate = new Date(filterValues.dateRange[1]).toISOString().split('T')[0];
    } else {
      // Default to current month
      startDate = firstDay;
      endDate = lastDay;
    }

    params.append('from', startDate);
    params.append('to', endDate);

    // Add pagination parameters
    params.append('page', page);
    params.append('size', size);

    // Add other filters if they have values
    if (filterValues.search) params.append('search', filterValues.search);
    if (filterValues.branch) params.append('branch_id', filterValues.branch);
    if (filterValues.department)
      params.append('department_id', filterValues.department);
    if (filterValues.role) params.append('role_id', filterValues.role);
    if (filterValues.user) params.append('user_id', filterValues.user);
    if (filterValues.shift_status)
      params.append('shift_status', filterValues.shift_status);

    return '?' + params.toString();
  };

  // Handle filter changes
  const handleApplyFilters = (values) => {
    let newFilters = { ...values };
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
    // Build API parameters and fetch data
    const filterParams = buildFilterParams(newFilters, 1, rowsPerPage);
    fetchRotaReportsList(filterParams, 1, rowsPerPage);
  };

  // Handle field change (especially for date_period and branch)
  const handleFieldChange = (name, value) => {
    if (name === 'date_period') {
      let updatedFilters = { ...filters, date_period: value };
      if (value === 'custom') {
        updatedFilters.dateRange = [null, null];
      } else if (value === 'week') {
        updatedFilters.dateRange = [weekStart, weekEnd];
      } else if (value === 'month') {
        updatedFilters.dateRange = [firstDay, lastDay];
      }
      setFilters(updatedFilters);
    } else if (name === 'branch') {
      setFilters((prev) => ({ ...prev, [name]: value, user: '' })); // Reset user when branch changes
      fetchUsers(value || '', filters.department || ''); // Fetch users for the selected branch
    } else if (name === 'department') {
      setFilters((prev) => ({ ...prev, [name]: value, user: '' })); // Reset user when branch changes
      fetchUsers(filters.branch || '', value || ''); // Fetch users for the selected branch
    } else {
      setFilters((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Pagination handlers
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
    const filterParams = buildFilterParams(filters, newPage, rowsPerPage);
    fetchRotaReportsList(filterParams, newPage, rowsPerPage);
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1); // Reset to first page when page size changes
    const filterParams = buildFilterParams(filters, 1, newRowsPerPage);
    fetchRotaReportsList(filterParams, 1, newRowsPerPage);
  };

  // Details view handlers
  const handleViewDetails = (row) => {
    setSelectedEmployee(row);
    setShowDetails(true);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedEmployee(null);
  };

  // Menu items for each row
  const menuItems = [
    {
      label: 'View',
      icon: <Icon name="Eye" size={16} />,
      onClick: (item, row) => {
        handleViewDetails(row);
      },
    },
  ];

  return (
    <Box className="report-main-container">
      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        initialValues={filters}
        onFieldChange={handleFieldChange}
      />
      {/* Your report content goes here */}

      <Box className="report-table-container">
        <CommonTable
          columns={columns}
          data={filteredData}
          actionMenuItems={menuItems}
          loading={loading}
          currentPage={currentPage}
          totalCount={totalCount}
          pageSize={rowsPerPage}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
        />
      </Box>

      {/* Details View Modal */}
      <RotaReportDetails
        employeeData={selectedEmployee}
        isOpen={showDetails}
        onClose={handleCloseDetails}
      />
    </Box>
  );
}
