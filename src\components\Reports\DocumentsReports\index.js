import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Alert,
  AlertTitle,
} from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import ContentLoader from '@/components/UI/ContentLoader';
import { DateFormat } from '@/helper/common/commonFunctions';
import { reportService } from '@/services/reportService';
import FolderIcon from '@mui/icons-material/Folder';
import StorageIcon from '@mui/icons-material/Storage';
import ImageIcon from '@mui/icons-material/Image';
import VideoFileIcon from '@mui/icons-material/VideoFile';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import AudioFileIcon from '@mui/icons-material/AudioFile';

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Helper function to get file type icon
const getFileTypeIcon = (fileType) => {
  const iconProps = { sx: { fontSize: 40, color: '#1976d2' } };
  switch (fileType?.toLowerCase()) {
    case 'image':
      return (
        <ImageIcon {...iconProps} sx={{ ...iconProps.sx, color: '#4caf50' }} />
      );
    case 'video':
      return (
        <VideoFileIcon
          {...iconProps}
          sx={{ ...iconProps.sx, color: '#f44336' }}
        />
      );
    case 'pdf':
      return (
        <PictureAsPdfIcon
          {...iconProps}
          sx={{ ...iconProps.sx, color: '#ff9800' }}
        />
      );
    case 'audio':
      return (
        <AudioFileIcon
          {...iconProps}
          sx={{ ...iconProps.sx, color: '#9c27b0' }}
        />
      );
    default:
      return <FolderIcon {...iconProps} />;
  }
};

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return '-';
  return DateFormat(dateString, 'datesWithhour');
};

export default function DocumentsReports() {
  // State management
  const [loading, setLoading] = useState(false);
  const [documentsData, setDocumentsData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [sortOrder, setSortOrder] = useState({
    field: 'created_at',
    direction: 'desc',
  });
  const [summary, setSummary] = useState(null);

  // Filter states
  const [filters, setFilters] = useState({
    search: '',
    file_type: '',
    size_range: '',
    date_range: { start: '', end: '' },
  });

  // Filter field configuration
  const filterFields = [
    {
      name: 'search',
      label: 'Search Documents',
      type: 'search',
      placeholder: 'Search by filename, type, or content...',
    },
    {
      name: 'file_type',
      label: 'File Type',
      type: 'select',
      placeholder: 'Select file type',
      options: [
        { label: 'All Types', value: '' },
        { label: 'Images', value: 'image' },
        { label: 'Videos', value: 'video' },
        { label: 'PDFs', value: 'pdf' },
        { label: 'Audio', value: 'audio' },
      ],
    },
    {
      name: 'size_range',
      label: 'File Size Range',
      type: 'select',
      placeholder: 'Select size range',
      options: [
        { label: 'All Sizes', value: '' },
        { label: 'Small (< 1MB)', value: 'small' },
        { label: 'Medium (1MB - 10MB)', value: 'medium' },
        { label: 'Large (10MB - 100MB)', value: 'large' },
        { label: 'Very Large (> 100MB)', value: 'xlarge' },
      ],
    },
    {
      name: 'date_range',
      label: 'Date Range',
      type: 'daterange',
      placeholder: 'Select date range',
    },
  ];

  // Table columns configuration
  const columns = [
    {
      header: 'File Name',
      accessor: 'filename',
      sortable: true,
      renderCell: (value, row) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: getFileTypeColor(row.file_type),
            }}
          />
          <span title={value}>{value}</span>
        </Box>
      ),
    },
    {
      header: 'Type',
      accessor: 'file_type',
      sortable: true,
      renderCell: (value) => (
        <span
          style={{
            textTransform: 'capitalize',
            padding: '4px 8px',
            borderRadius: '4px',
            backgroundColor: getFileTypeColor(value) + '20',
            color: getFileTypeColor(value),
            fontSize: '12px',
            fontWeight: 'bold',
          }}
        >
          {value}
        </span>
      ),
    },
    {
      header: 'Size',
      accessor: 'size',
      sortable: true,
      renderCell: (value) => formatFileSize(value),
    },
    {
      header: 'Upload Date',
      accessor: 'created_at',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'Last Modified',
      accessor: 'updated_at',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
  ];

  // Helper function to get file type colors
  const getFileTypeColor = (fileType) => {
    const colors = {
      image: '#4CAF50',
      video: '#2196F3',
      pdf: '#F44336',
      audio: '#FF9800',
      default: '#757575',
    };
    return colors[fileType] || colors.default;
  };

  // Fetch subscription usage data (contains summary and file distribution)
  const fetchSubscriptionUsage = useCallback(async () => {
    setLoading(true);
    try {
      const response = await reportService.getDocumentsReports();

      if (response && response.data) {
        // Set summary data
        setSummary(response.data);

        // Generate table data from file_type_distribution
        if (response.data.file_type_distribution) {
          const tableData = response.data.file_type_distribution.map(
            (item, index) => ({
              id: index + 1,
              filename: `${item.item_type.charAt(0).toUpperCase() + item.item_type.slice(1)} Files`,
              file_type: item.item_type,
              size: item.size,
              count: item.count,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            })
          );

          // Apply filters to the data
          const filteredData = applyFiltersToData(tableData, filters);

          // Apply sorting
          const sortedData = applySortingToData(filteredData, sortOrder);

          // Apply pagination
          const paginatedData = applyPaginationToData(
            sortedData,
            page,
            rowsPerPage
          );

          setDocumentsData(paginatedData);
          setTotalCount(filteredData.length);
        } else {
          setDocumentsData([]);
          setTotalCount(0);
        }
      } else {
        setDocumentsData([]);
        setTotalCount(0);
        setSummary(null);
      }
    } catch (error) {
      console.error('Error fetching subscription usage:', error);
      setDocumentsData([]);
      setTotalCount(0);
      setSummary(null);
    } finally {
      setLoading(false);
    }
  }, [filters, page, rowsPerPage, sortOrder]);

  // Helper functions for data manipulation
  const applyFiltersToData = (data, filterParams) => {
    return data.filter((item) => {
      // Search filter
      if (filterParams.search) {
        const searchLower = filterParams.search.toLowerCase();
        if (!item.filename.toLowerCase().includes(searchLower)) {
          return false;
        }
      }

      // File type filter
      if (filterParams.file_type && filterParams.file_type !== 'all') {
        if (item.file_type !== filterParams.file_type) {
          return false;
        }
      }

      // Size range filter
      if (filterParams.size_range) {
        const sizeInMB = item.size / (1024 * 1024);
        switch (filterParams.size_range) {
          case 'small':
            if (sizeInMB >= 10) return false;
            break;
          case 'medium':
            if (sizeInMB < 10 || sizeInMB >= 100) return false;
            break;
          case 'large':
            if (sizeInMB < 100 || sizeInMB >= 1000) return false;
            break;
          case 'very_large':
            if (sizeInMB < 1000) return false;
            break;
        }
      }

      return true;
    });
  };

  const applySortingToData = (data, sortOrder) => {
    if (!sortOrder.field) return data;

    return [...data].sort((a, b) => {
      let aValue = a[sortOrder.field];
      let bValue = b[sortOrder.field];

      // Handle different data types
      if (sortOrder.field === 'size' || sortOrder.field === 'count') {
        aValue = Number(aValue);
        bValue = Number(bValue);
      } else if (
        sortOrder.field === 'created_at' ||
        sortOrder.field === 'updated_at'
      ) {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      } else {
        aValue = String(aValue).toLowerCase();
        bValue = String(bValue).toLowerCase();
      }

      if (sortOrder.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  };

  const applyPaginationToData = (data, pageNum, pageSize) => {
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return data.slice(startIndex, endIndex);
  };

  // Event handlers
  const handleApplyFilters = (newFilters) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filters change
    fetchSubscriptionUsage();
  };

  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchSubscriptionUsage();
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    fetchSubscriptionUsage();
  };

  const handleSort = (field, direction) => {
    const newSortOrder = { field, direction };
    setSortOrder(newSortOrder);
    fetchSubscriptionUsage();
  };

  // Initial data fetch
  useEffect(() => {
    fetchSubscriptionUsage();
  }, []);

  return (
    <Box className="report-main-container">
      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        buttonText="Apply Filters"
        initialValues={filters}
      />

      {/* Summary Section - Below Filter Component */}
      {summary ? (
        <Box sx={{ mb: 3 }}>
          <Typography
            variant="h5"
            sx={{ mb: 3, fontWeight: 600, color: '#1976d2' }}
          >
            📊 Storage Overview
          </Typography>

          {/* Main Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  background:
                    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    transition: 'all 0.3s ease',
                  },
                }}
              >
                <CardContent sx={{ textAlign: 'center', py: 3 }}>
                  <FolderIcon sx={{ fontSize: 48, mb: 1, opacity: 0.9 }} />
                  <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {summary.total_files?.toLocaleString() || '0'}
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.9 }}>
                    Total Files
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  background:
                    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  color: 'white',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    transition: 'all 0.3s ease',
                  },
                }}
              >
                <CardContent sx={{ textAlign: 'center', py: 3 }}>
                  <StorageIcon sx={{ fontSize: 48, mb: 1, opacity: 0.9 }} />
                  <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {summary.total_size} {summary.unit}
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.9 }}>
                    Total Storage
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  background:
                    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                  color: 'white',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    transition: 'all 0.3s ease',
                  },
                }}
              >
                <CardContent sx={{ textAlign: 'center', py: 3 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {formatFileSize(summary.raw_bytes)}
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.9 }}>
                    Raw Storage
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{ opacity: 0.8, display: 'block', mt: 1 }}
                  >
                    ({summary.raw_bytes?.toLocaleString()} bytes)
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  background:
                    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                  color: 'white',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    transition: 'all 0.3s ease',
                  },
                }}
              >
                <CardContent sx={{ textAlign: 'center', py: 3 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {summary.file_type_distribution?.length || 0}
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.9 }}>
                    File Types
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* File Type Distribution */}
          {summary.file_type_distribution &&
            summary.file_type_distribution.length > 0 && (
              <Box>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  📁 File Type Breakdown
                </Typography>
                <Grid container spacing={2}>
                  {summary.file_type_distribution.map((type) => (
                    <Grid item xs={12} sm={6} md={3} key={type.item_type}>
                      <Card
                        sx={{
                          height: '100%',
                          border: '1px solid #e0e0e0',
                          '&:hover': {
                            boxShadow: 3,
                            transform: 'translateY(-1px)',
                            transition: 'all 0.2s ease',
                          },
                        }}
                      >
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                          <Box sx={{ mb: 1 }}>
                            {getFileTypeIcon(type.item_type)}
                          </Box>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 600,
                              mb: 1,
                              textTransform: 'capitalize',
                            }}
                          >
                            {type.item_type}
                          </Typography>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              mb: 1,
                            }}
                          >
                            <Chip
                              label={`${type.count} files`}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                            <Chip
                              label={formatFileSize(type.size)}
                              size="small"
                              color="secondary"
                              variant="outlined"
                            />
                          </Box>
                          <Typography variant="body2" color="textSecondary">
                            Avg:{' '}
                            {formatFileSize(Math.round(type.size / type.count))}{' '}
                            per file
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
        </Box>
      ) : (
        !loading && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <AlertTitle>No Data Available</AlertTitle>
            No storage usage data found. Please check your connection or try
            again later.
          </Alert>
        )
      )}

      <Box className="report-table-container">
        {loading ? (
          <ContentLoader />
        ) : documentsData && documentsData.length > 0 ? (
          <CommonTable
            columns={columns}
            data={documentsData}
            totalCount={totalCount}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
            showPagination={true}
            onSort={handleSort}
            sortOrder={sortOrder}
          />
        ) : (
          <Alert severity="info" sx={{ mt: 2 }}>
            <AlertTitle>No File Data Available</AlertTitle>
            {summary
              ? 'File type distribution data is not available in the current response.'
              : 'No storage data found. Please try refreshing the page.'}
          </Alert>
        )}
      </Box>
    </Box>
  );
}
