import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Alert, AlertTitle } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import ContentLoader from '@/components/UI/ContentLoader';
import { DateFormat } from '@/helper/common/commonFunctions';
import { reportService } from '@/services/reportService';

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return '-';
  return DateFormat(dateString, 'datesWithhour');
};

export default function DocumentsReports() {
  // State management
  const [loading, setLoading] = useState(false);
  const [documentsData, setDocumentsData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [sortOrder, setSortOrder] = useState({
    field: 'created_at',
    direction: 'desc',
  });
  const [summary, setSummary] = useState(null);

  // Filter states
  const [filters, setFilters] = useState({
    search: '',
    file_type: '',
    size_range: '',
    date_range: { start: '', end: '' },
  });

  // Filter field configuration
  const filterFields = [
    {
      name: 'search',
      label: 'Search Documents',
      type: 'search',
      placeholder: 'Search by filename, type, or content...',
    },
    {
      name: 'file_type',
      label: 'File Type',
      type: 'select',
      placeholder: 'Select file type',
      options: [
        { label: 'All Types', value: '' },
        { label: 'Images', value: 'image' },
        { label: 'Videos', value: 'video' },
        { label: 'PDFs', value: 'pdf' },
        { label: 'Audio', value: 'audio' },
      ],
    },
    {
      name: 'size_range',
      label: 'File Size Range',
      type: 'select',
      placeholder: 'Select size range',
      options: [
        { label: 'All Sizes', value: '' },
        { label: 'Small (< 1MB)', value: 'small' },
        { label: 'Medium (1MB - 10MB)', value: 'medium' },
        { label: 'Large (10MB - 100MB)', value: 'large' },
        { label: 'Very Large (> 100MB)', value: 'xlarge' },
      ],
    },
    {
      name: 'date_range',
      label: 'Date Range',
      type: 'daterange',
      placeholder: 'Select date range',
    },
  ];

  // Table columns configuration
  const columns = [
    {
      header: 'File Name',
      accessor: 'filename',
      sortable: true,
      renderCell: (value, row) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: getFileTypeColor(row.file_type),
            }}
          />
          <span title={value}>{value}</span>
        </Box>
      ),
    },
    {
      header: 'Type',
      accessor: 'file_type',
      sortable: true,
      renderCell: (value) => (
        <span
          style={{
            textTransform: 'capitalize',
            padding: '4px 8px',
            borderRadius: '4px',
            backgroundColor: getFileTypeColor(value) + '20',
            color: getFileTypeColor(value),
            fontSize: '12px',
            fontWeight: 'bold',
          }}
        >
          {value}
        </span>
      ),
    },
    {
      header: 'Size',
      accessor: 'size',
      sortable: true,
      renderCell: (value) => formatFileSize(value),
    },
    {
      header: 'Upload Date',
      accessor: 'created_at',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'Last Modified',
      accessor: 'updated_at',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
  ];

  // Helper function to get file type colors
  const getFileTypeColor = (fileType) => {
    const colors = {
      image: '#4CAF50',
      video: '#2196F3',
      pdf: '#F44336',
      audio: '#FF9800',
      default: '#757575',
    };
    return colors[fileType] || colors.default;
  };

  // Fetch subscription usage data (contains summary and file distribution)
  const fetchSubscriptionUsage = useCallback(async () => {
    setLoading(true);
    try {
      const response = await reportService.getDocumentsReports();

      if (response && response.data) {
        // Set summary data
        setSummary(response.data);

        // Generate table data from file_type_distribution
        if (response.data.file_type_distribution) {
          const tableData = response.data.file_type_distribution.map(
            (item, index) => ({
              id: index + 1,
              filename: `${item.item_type.charAt(0).toUpperCase() + item.item_type.slice(1)} Files`,
              file_type: item.item_type,
              size: item.size,
              count: item.count,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            })
          );

          // Apply filters to the data
          const filteredData = applyFiltersToData(tableData, filters);

          // Apply sorting
          const sortedData = applySortingToData(filteredData, sortOrder);

          // Apply pagination
          const paginatedData = applyPaginationToData(
            sortedData,
            page,
            rowsPerPage
          );

          setDocumentsData(paginatedData);
          setTotalCount(filteredData.length);
        } else {
          setDocumentsData([]);
          setTotalCount(0);
        }
      } else {
        setDocumentsData([]);
        setTotalCount(0);
        setSummary(null);
      }
    } catch (error) {
      console.error('Error fetching subscription usage:', error);
      setDocumentsData([]);
      setTotalCount(0);
      setSummary(null);
    } finally {
      setLoading(false);
    }
  }, [filters, page, rowsPerPage, sortOrder]);

  // Helper functions for data manipulation
  const applyFiltersToData = (data, filterParams) => {
    return data.filter((item) => {
      // Search filter
      if (filterParams.search) {
        const searchLower = filterParams.search.toLowerCase();
        if (!item.filename.toLowerCase().includes(searchLower)) {
          return false;
        }
      }

      // File type filter
      if (filterParams.file_type && filterParams.file_type !== 'all') {
        if (item.file_type !== filterParams.file_type) {
          return false;
        }
      }

      // Size range filter
      if (filterParams.size_range) {
        const sizeInMB = item.size / (1024 * 1024);
        switch (filterParams.size_range) {
          case 'small':
            if (sizeInMB >= 10) return false;
            break;
          case 'medium':
            if (sizeInMB < 10 || sizeInMB >= 100) return false;
            break;
          case 'large':
            if (sizeInMB < 100 || sizeInMB >= 1000) return false;
            break;
          case 'very_large':
            if (sizeInMB < 1000) return false;
            break;
        }
      }

      return true;
    });
  };

  const applySortingToData = (data, sortOrder) => {
    if (!sortOrder.field) return data;

    return [...data].sort((a, b) => {
      let aValue = a[sortOrder.field];
      let bValue = b[sortOrder.field];

      // Handle different data types
      if (sortOrder.field === 'size' || sortOrder.field === 'count') {
        aValue = Number(aValue);
        bValue = Number(bValue);
      } else if (
        sortOrder.field === 'created_at' ||
        sortOrder.field === 'updated_at'
      ) {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      } else {
        aValue = String(aValue).toLowerCase();
        bValue = String(bValue).toLowerCase();
      }

      if (sortOrder.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  };

  const applyPaginationToData = (data, pageNum, pageSize) => {
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return data.slice(startIndex, endIndex);
  };

  // Event handlers
  const handleApplyFilters = (newFilters) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filters change
    fetchSubscriptionUsage();
  };

  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchSubscriptionUsage();
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    fetchSubscriptionUsage();
  };

  const handleSort = (field, direction) => {
    const newSortOrder = { field, direction };
    setSortOrder(newSortOrder);
    fetchSubscriptionUsage();
  };

  // Initial data fetch
  useEffect(() => {
    fetchSubscriptionUsage();
  }, []);

  return (
    <Box className="report-main-container">
      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        buttonText="Apply Filters"
        initialValues={filters}
      />

      {/* Simple Summary Section - Below Filter Component */}
      {summary ? (
        <Box
          sx={{
            mb: 3,
            p: 2,
            backgroundColor: '#f8f9fa',
            borderRadius: 1,
            border: '1px solid #e9ecef',
          }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Storage Summary
          </Typography>

          <Box
            sx={{
              display: 'flex',
              gap: 4,
              flexWrap: 'wrap',
              alignItems: 'center',
            }}
          >
            <Box>
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{ mb: 0.5 }}
              >
                Total Files
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {summary.total_files?.toLocaleString() || '0'}
              </Typography>
            </Box>

            <Box>
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{ mb: 0.5 }}
              >
                Total Size
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {summary.total_size} {summary.unit}
              </Typography>
            </Box>

            {summary.file_type_distribution?.map((type) => (
              <Box key={type.item_type}>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  sx={{ mb: 0.5, textTransform: 'capitalize' }}
                >
                  {type.item_type} Files
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  {type.count} ({formatFileSize(type.size)})
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      ) : (
        !loading && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <AlertTitle>No Data Available</AlertTitle>
            No storage usage data found. Please check your connection or try
            again later.
          </Alert>
        )
      )}

      <Box className="report-table-container">
        {loading ? (
          <ContentLoader />
        ) : documentsData && documentsData.length > 0 ? (
          <CommonTable
            columns={columns}
            data={documentsData}
            totalCount={totalCount}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
            showPagination={true}
            onSort={handleSort}
            sortOrder={sortOrder}
          />
        ) : (
          <Alert severity="info" sx={{ mt: 2 }}>
            <AlertTitle>No File Data Available</AlertTitle>
            {summary
              ? 'File type distribution data is not available in the current response.'
              : 'No storage data found. Please try refreshing the page.'}
          </Alert>
        )}
      </Box>
    </Box>
  );
}
