import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Button } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import ContentLoader from '@/components/UI/ContentLoader';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';
import { documentsService } from '@/services/documentsService';
import DownloadIcon from '@mui/icons-material/Download';

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return '-';
  return DateFormat(dateString, 'datesWithhour');
};

export default function DocumentsReports() {
  // State management
  const [loading, setLoading] = useState(false);
  const [documentsData, setDocumentsData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [sortOrder, setSortOrder] = useState({
    field: 'created_at',
    direction: 'desc',
  });
  const [summary, setSummary] = useState(null);
  const [exporting, setExporting] = useState(false);

  // Filter states
  const [filters, setFilters] = useState({
    search: '',
    file_type: '',
    size_range: '',
    date_range: { start: '', end: '' },
  });

  // Filter field configuration
  const filterFields = [
    {
      name: 'search',
      label: 'Search Documents',
      type: 'search',
      placeholder: 'Search by filename, type, or content...',
    },
    {
      name: 'file_type',
      label: 'File Type',
      type: 'select',
      placeholder: 'Select file type',
      options: [
        { label: 'All Types', value: '' },
        { label: 'Images', value: 'image' },
        { label: 'Videos', value: 'video' },
        { label: 'PDFs', value: 'pdf' },
        { label: 'Audio', value: 'audio' },
      ],
    },
    {
      name: 'size_range',
      label: 'File Size Range',
      type: 'select',
      placeholder: 'Select size range',
      options: [
        { label: 'All Sizes', value: '' },
        { label: 'Small (< 1MB)', value: 'small' },
        { label: 'Medium (1MB - 10MB)', value: 'medium' },
        { label: 'Large (10MB - 100MB)', value: 'large' },
        { label: 'Very Large (> 100MB)', value: 'xlarge' },
      ],
    },
    {
      name: 'date_range',
      label: 'Date Range',
      type: 'daterange',
      placeholder: 'Select date range',
    },
  ];

  // Table columns configuration
  const columns = [
    {
      header: 'File Name',
      accessor: 'filename',
      sortable: true,
      renderCell: (value, row) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: getFileTypeColor(row.file_type),
            }}
          />
          <span title={value}>{value}</span>
        </Box>
      ),
    },
    {
      header: 'Type',
      accessor: 'file_type',
      sortable: true,
      renderCell: (value) => (
        <span
          style={{
            textTransform: 'capitalize',
            padding: '4px 8px',
            borderRadius: '4px',
            backgroundColor: getFileTypeColor(value) + '20',
            color: getFileTypeColor(value),
            fontSize: '12px',
            fontWeight: 'bold',
          }}
        >
          {value}
        </span>
      ),
    },
    {
      header: 'Size',
      accessor: 'size',
      sortable: true,
      renderCell: (value) => formatFileSize(value),
    },
    {
      header: 'Upload Date',
      accessor: 'created_at',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'Last Modified',
      accessor: 'updated_at',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
  ];

  // Helper function to get file type colors
  const getFileTypeColor = (fileType) => {
    const colors = {
      image: '#4CAF50',
      video: '#2196F3',
      pdf: '#F44336',
      audio: '#FF9800',
      default: '#757575',
    };
    return colors[fileType] || colors.default;
  };

  // API call to fetch documents data
  const fetchDocumentsData = useCallback(
    async (
      filterParams = filters,
      pageNum = page,
      pageSize = rowsPerPage,
      sortBy = sortOrder.field,
      sortDirection = sortOrder.direction
    ) => {
      setLoading(true);
      try {
        // Build query parameters object
        const params = {
          page: pageNum.toString(),
          size: pageSize.toString(),
        };

        // Add sorting
        if (sortBy) {
          params.sort_by = sortBy;
          params.sort_order = sortDirection;
        }

        // Add filters
        if (filterParams.search) {
          params.search = filterParams.search;
        }
        if (filterParams.file_type) {
          params.file_type = filterParams.file_type;
        }
        if (filterParams.size_range) {
          params.size_range = filterParams.size_range;
        }
        if (filterParams.date_range?.start && filterParams.date_range?.end) {
          params.start_date = filterParams.date_range.start;
          params.end_date = filterParams.date_range.end;
        }

        // Make API call using service
        const response = await documentsService.getDocumentsReports(params);

        if (response.success) {
          setDocumentsData(response.data);
          setTotalCount(response.totalCount);
        } else {
          // Fallback to mock data
          const mockData = generateMockDocumentsData(
            filterParams,
            pageNum,
            pageSize
          );
          setDocumentsData(mockData.data);
          setTotalCount(mockData.totalCount);
        }
      } catch (error) {
        console.error('Error fetching documents data:', error);
        // Use mock data for demonstration
        const mockData = generateMockDocumentsData(
          filterParams,
          pageNum,
          pageSize
        );
        setDocumentsData(mockData.data);
        setTotalCount(mockData.totalCount);
      } finally {
        setLoading(false);
      }
    },
    [filters, page, rowsPerPage, sortOrder]
  );

  // Generate mock data based on the provided structure
  const generateMockDocumentsData = (
    filterParams = filters,
    pageNum = 1,
    pageSize = 10
  ) => {
    // Mock data based on your provided structure
    const mockFiles = [
      // Images
      ...Array.from({ length: 20 }, (_, i) => ({
        id: `img_${i + 1}`,
        filename: `image_${i + 1}.jpg`,
        file_type: 'image',
        size: Math.floor(Math.random() * 5000000) + 100000, // 100KB to 5MB
        created_at: new Date(
          Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000
        ).toISOString(),
        updated_at: new Date(
          Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
      })),
      // Videos
      ...Array.from({ length: 8 }, (_, i) => ({
        id: `vid_${i + 1}`,
        filename: `video_${i + 1}.mp4`,
        file_type: 'video',
        size: Math.floor(Math.random() * 50000000) + 10000000, // 10MB to 60MB
        created_at: new Date(
          Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000
        ).toISOString(),
        updated_at: new Date(
          Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
      })),
      // PDFs
      ...Array.from({ length: 25 }, (_, i) => ({
        id: `pdf_${i + 1}`,
        filename: `document_${i + 1}.pdf`,
        file_type: 'pdf',
        size: Math.floor(Math.random() * 10000000) + 500000, // 500KB to 10MB
        created_at: new Date(
          Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000
        ).toISOString(),
        updated_at: new Date(
          Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
      })),
      // Audio
      ...Array.from({ length: 3 }, (_, i) => ({
        id: `aud_${i + 1}`,
        filename: `audio_${i + 1}.mp3`,
        file_type: 'audio',
        size: Math.floor(Math.random() * 8000000) + 1000000, // 1MB to 9MB
        created_at: new Date(
          Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000
        ).toISOString(),
        updated_at: new Date(
          Date.now() - Math.random() * 30 * 24 * 60 * 1000
        ).toISOString(),
      })),
    ];

    // Apply filters
    let filteredFiles = mockFiles;

    if (filterParams.search) {
      filteredFiles = filteredFiles.filter(
        (file) =>
          file.filename
            .toLowerCase()
            .includes(filterParams.search.toLowerCase()) ||
          file.file_type
            .toLowerCase()
            .includes(filterParams.search.toLowerCase())
      );
    }

    if (filterParams.file_type) {
      filteredFiles = filteredFiles.filter(
        (file) => file.file_type === filterParams.file_type
      );
    }

    if (filterParams.size_range) {
      filteredFiles = filteredFiles.filter((file) => {
        const size = file.size;
        switch (filterParams.size_range) {
          case 'small':
            return size < 1000000; // < 1MB
          case 'medium':
            return size >= 1000000 && size <= 10000000; // 1MB - 10MB
          case 'large':
            return size > 10000000 && size <= 100000000; // 10MB - 100MB
          case 'xlarge':
            return size > 100000000; // > 100MB
          default:
            return true;
        }
      });
    }

    // Apply date range filter if provided
    if (filterParams.date_range?.start && filterParams.date_range?.end) {
      const startDate = new Date(filterParams.date_range.start);
      const endDate = new Date(filterParams.date_range.end);
      filteredFiles = filteredFiles.filter((file) => {
        const fileDate = new Date(file.created_at);
        return fileDate >= startDate && fileDate <= endDate;
      });
    }

    // Apply sorting
    filteredFiles.sort((a, b) => {
      const aValue = a[sortOrder.field];
      const bValue = b[sortOrder.field];

      if (sortOrder.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // Apply pagination
    const startIndex = (pageNum - 1) * pageSize;
    const paginatedFiles = filteredFiles.slice(
      startIndex,
      startIndex + pageSize
    );

    return {
      data: paginatedFiles,
      totalCount: filteredFiles.length,
    };
  };

  // Event handlers
  const handleApplyFilters = (newFilters) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filters change
    fetchDocumentsData(newFilters, 1, rowsPerPage);
  };

  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchDocumentsData(filters, newPage, rowsPerPage);
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    fetchDocumentsData(filters, 1, newRowsPerPage);
  };

  const handleSort = (field, direction) => {
    const newSortOrder = { field, direction };
    setSortOrder(newSortOrder);
    fetchDocumentsData(filters, page, rowsPerPage, field, direction);
  };

  // Fetch summary data
  const fetchSummary = useCallback(async () => {
    try {
      const response = await documentsService.getDocumentsSummary();
      if (response.success) {
        setSummary(response.data);
      }
    } catch (error) {
      console.error('Error fetching summary:', error);
      // Use mock summary data
      setSummary({
        total_size: 100.41,
        unit: 'MB',
        raw_bytes: 105289484,
        total_files: 130,
        file_type_distribution: [
          { item_type: 'image', count: 94, size: 48853702 },
          { item_type: 'video', count: 8, size: 45965768 },
          { item_type: 'pdf', count: 25, size: 8861629 },
          { item_type: 'audio', count: 3, size: 1608385 },
        ],
      });
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchDocumentsData();
    fetchSummary();
  }, []);

  return (
    <Box className="report-main-container">
      {/* Summary Section */}
      {summary && (
        <Box sx={{ mb: 3, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Documents Summary
          </Typography>
          <Box sx={{ display: 'flex', gap: 4, flexWrap: 'wrap' }}>
            <Box>
              <Typography variant="body2" color="textSecondary">
                Total Files
              </Typography>
              <Typography variant="h6">{summary.total_files}</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="textSecondary">
                Total Size
              </Typography>
              <Typography variant="h6">
                {summary.total_size} {summary.unit}
              </Typography>
            </Box>
            {summary.file_type_distribution?.map((type) => (
              <Box key={type.item_type}>
                <Typography variant="body2" color="textSecondary">
                  {type.item_type.charAt(0).toUpperCase() +
                    type.item_type.slice(1)}
                  s
                </Typography>
                <Typography variant="h6">
                  {type.count} ({formatFileSize(type.size)})
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      )}

      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        buttonText="Apply Filters"
        initialValues={filters}
      />

      <Box className="report-table-container">
        {loading ? (
          <ContentLoader />
        ) : (
          <CommonTable
            columns={columns}
            data={documentsData}
            totalCount={totalCount}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
            showPagination={true}
            onSort={handleSort}
            sortOrder={sortOrder}
          />
        )}
      </Box>
    </Box>
  );
}
