import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
  AlertTitle,
  CircularProgress,
} from '@mui/material';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import { reportService } from '@/services/reportService';

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export default function DocumentsReports() {
  // State management
  const [loading, setLoading] = useState(false);
  const [summary, setSummary] = useState(null);
  const [documentsData, setDocumentsData] = useState([]);

  // Table columns configuration
  const columns = [
    {
      header: 'File Type',
      accessor: 'file_type',
      sortable: true,
      renderCell: (value) => (
        <span
          style={{
            textTransform: 'capitalize',
            padding: '4px 8px',
            borderRadius: '4px',
            backgroundColor: getFileTypeColor(value) + '20',
            color: getFileTypeColor(value),
            fontSize: '12px',
            fontWeight: 'bold',
          }}
        >
          {value}
        </span>
      ),
    },
    {
      header: 'File Count',
      accessor: 'count',
      sortable: true,
      renderCell: (value) => value?.toLocaleString() || '0',
    },
    {
      header: 'Total Size',
      accessor: 'size',
      sortable: true,
      renderCell: (value) => formatFileSize(value),
    },
    {
      header: 'Average Size',
      accessor: 'average_size',
      sortable: true,
      renderCell: (value) => formatFileSize(value),
    },
  ];

  // Helper function to get file type colors
  const getFileTypeColor = (fileType) => {
    const colors = {
      image: '#4CAF50',
      video: '#2196F3',
      pdf: '#F44336',
      audio: '#FF9800',
      default: '#757575',
    };
    return colors[fileType] || colors.default;
  };

  // Fetch subscription usage data (contains summary and file distribution)
  const fetchSubscriptionUsage = async () => {
    setLoading(true);
    try {
      const response = await reportService.getDocumentsReports();

      if (response && response.data) {
        setSummary(response.data);

        // Generate table data from file_type_distribution
        if (response.data.file_type_distribution) {
          const tableData = response.data.file_type_distribution.map(
            (item, index) => ({
              id: index + 1,
              file_type: item.item_type,
              count: item.count,
              size: item.size,
              average_size: Math.round(item.size / item.count),
            })
          );
          setDocumentsData(tableData);
        } else {
          setDocumentsData([]);
        }
      } else {
        setSummary(null);
        setDocumentsData([]);
      }
    } catch (error) {
      console.error('Error fetching subscription usage:', error);
      setSummary(null);
      setDocumentsData([]);
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchSubscriptionUsage();
  }, []);

  return (
    <Box className="report-main-container">
      {/* Simple Summary Section */}
      {loading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            py: 4,
          }}
        >
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Loading storage data...
          </Typography>
        </Box>
      ) : summary ? (
        <Box
          sx={{
            mb: 3,
            p: 2,
            backgroundColor: '#f8f9fa',
            borderRadius: 1,
            border: '1px solid #e9ecef',
          }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Storage Summary
          </Typography>

          <Box
            sx={{
              display: 'flex',
              gap: 4,
              flexWrap: 'wrap',
              alignItems: 'center',
            }}
          >
            <Box>
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{ mb: 0.5 }}
              >
                Total Files
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {summary.total_files?.toLocaleString() || '0'}
              </Typography>
            </Box>

            <Box>
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{ mb: 0.5 }}
              >
                Total Size
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {summary.total_size} {summary.unit}
              </Typography>
            </Box>

            {summary.file_type_distribution?.map((type) => (
              <Box key={type.item_type}>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  sx={{ mb: 0.5, textTransform: 'capitalize' }}
                >
                  {type.item_type} Files
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  {type.count} ({formatFileSize(type.size)})
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      ) : (
        <Alert severity="info" sx={{ mb: 3 }}>
          <AlertTitle>No Storage Data Available</AlertTitle>
          No storage usage data found. Please check your connection or try again
          later.
        </Alert>
      )}
    </Box>
  );
}
