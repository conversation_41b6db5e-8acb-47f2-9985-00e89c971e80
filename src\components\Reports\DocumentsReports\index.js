import React, { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import NoDataView from '@/components/UI/NoDataView';
import PreLoader from '@/components/UI/Loader';
import CommonNoDataImage from '@/components/Recipes/Recipes/CommonNoDataImage';
import { reportService } from '@/services/reportService';
import './DocumentsReports.scss';

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export default function DocumentsReports() {
  // State management
  const [loading, setLoading] = useState(false);
  const [summary, setSummary] = useState(null);
  const [documentsData, setDocumentsData] = useState([]);

  // Table columns configuration
  const columns = [
    {
      header: 'File Type',
      accessor: 'file_type',
      sortable: false,
      renderCell: (value) => (
        <span
          className={`documents-reports__file-type-badge documents-reports__file-type--${value || 'default'}`}
        >
          {value || 'Unknown'}
        </span>
      ),
    },
    {
      header: 'File Count',
      accessor: 'count',
      sortable: false,
      renderCell: (value) => (value || 0).toLocaleString(),
    },
    {
      header: 'Total Size',
      accessor: 'size',
      sortable: false,
      renderCell: (value) => formatFileSize(value || 0),
    },
    {
      header: 'Average Size',
      accessor: 'average_size',
      sortable: false,
      renderCell: (value) => formatFileSize(value || 0),
    },
  ];

  // Fetch subscription usage data (contains summary and file distribution)
  const fetchSubscriptionUsage = async () => {
    setLoading(true);
    try {
      const response = await reportService.getDocumentsReports();

      if (response?.data) {
        setSummary(response.data);

        // Generate table data from file_type_distribution using optional chaining
        if (response.data?.file_type_distribution?.length > 0) {
          const tableData = response.data.file_type_distribution.map(
            (item, index) => ({
              id: index + 1,
              file_type: item?.item_type || 'unknown',
              count: item?.count || 0,
              size: item?.size || 0,
              average_size:
                item?.count > 0
                  ? Math.round((item?.size || 0) / item.count)
                  : 0,
            })
          );
          setDocumentsData(tableData);
        } else {
          setDocumentsData([]);
        }
      } else {
        setSummary(null);
        setDocumentsData([]);
      }
    } catch (error) {
      console.error('Error fetching subscription usage:', error);
      setSummary(null);
      setDocumentsData([]);
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchSubscriptionUsage();
  }, []);

  return (
    <Box className="documents-reports__container">
      {/* Loading State */}
      {loading && <PreLoader />}

      {/* Simple Summary Section */}
      {!loading && summary ? (
        <Box className="documents-reports__summary">
          <Box className="documents-reports__summary-grid">
            <Box className="documents-reports__summary-item">
              <Typography
                variant="body2"
                className="documents-reports__summary-item-label"
              >
                Total Files
              </Typography>
              <Typography
                variant="h6"
                className="documents-reports__summary-item-value"
              >
                {summary?.total_files?.toLocaleString() || '0'}
              </Typography>
            </Box>

            <Box className="documents-reports__summary-item">
              <Typography
                variant="body2"
                className="documents-reports__summary-item-label"
              >
                Total Size
              </Typography>
              <Typography
                variant="h6"
                className="documents-reports__summary-item-value"
              >
                {summary?.total_size || '0'} {summary?.unit || 'B'}
              </Typography>
            </Box>

            {summary?.file_type_distribution?.map((type) => (
              <Box
                key={type?.item_type}
                className="documents-reports__summary-item"
              >
                <Typography
                  variant="body2"
                  className="documents-reports__summary-item-label"
                >
                  {type?.item_type || 'Unknown'} Files
                </Typography>
                <Typography
                  variant="h6"
                  className="documents-reports__summary-item-value"
                >
                  {type?.count || 0} ({formatFileSize(type?.size || 0)})
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      ) : (
        !loading && (
          <Box className="documents-reports__no-data">
            <NoDataView
              image={<CommonNoDataImage />}
              title="No Storage Data Available"
              description="No storage usage data found. Please check your connection or try again later."
              className="no-data-auto-margin-height-conainer"
            />
          </Box>
        )
      )}

      {/* File Type Distribution Table */}
      {!loading && documentsData?.length > 0 && (
        <Box className="documents-reports__table-container">
          <CommonTable
            columns={columns}
            data={documentsData}
            showPagination={false}
            sortable={true}
          />
        </Box>
      )}
    </Box>
  );
}
