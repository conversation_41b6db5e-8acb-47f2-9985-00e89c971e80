import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
  AlertTitle,
  CircularProgress,
} from '@mui/material';
import { reportService } from '@/services/reportService';

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export default function DocumentsReports() {
  // State management
  const [loading, setLoading] = useState(false);
  const [summary, setSummary] = useState(null);

  // Fetch subscription usage data (contains summary only)
  const fetchSubscriptionUsage = async () => {
    setLoading(true);
    try {
      const response = await reportService.getDocumentsReports();

      if (response && response.data) {
        setSummary(response.data);
      } else {
        setSummary(null);
      }
    } catch (error) {
      console.error('Error fetching subscription usage:', error);
      setSummary(null);
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchSubscriptionUsage();
  }, []);

  return (
    <Box className="report-main-container">
      {/* Simple Summary Section */}
      {loading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            py: 4,
          }}
        >
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Loading storage data...
          </Typography>
        </Box>
      ) : summary ? (
        <Box
          sx={{
            mb: 3,
            p: 2,
            backgroundColor: '#f8f9fa',
            borderRadius: 1,
            border: '1px solid #e9ecef',
          }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Storage Summary
          </Typography>

          <Box
            sx={{
              display: 'flex',
              gap: 4,
              flexWrap: 'wrap',
              alignItems: 'center',
            }}
          >
            <Box>
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{ mb: 0.5 }}
              >
                Total Files
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {summary.total_files?.toLocaleString() || '0'}
              </Typography>
            </Box>

            <Box>
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{ mb: 0.5 }}
              >
                Total Size
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {summary.total_size} {summary.unit}
              </Typography>
            </Box>

            {summary.file_type_distribution?.map((type) => (
              <Box key={type.item_type}>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  sx={{ mb: 0.5, textTransform: 'capitalize' }}
                >
                  {type.item_type} Files
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  {type.count} ({formatFileSize(type.size)})
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      ) : (
        !loading && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <AlertTitle>No Data Available</AlertTitle>
            No storage usage data found. Please check your connection or try
            again later.
          </Alert>
        )
      )}
    </Box>
  );
}
