'use client';
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Avatar,
  Card,
  CardContent,
  Grid,
  Divider,
} from '@mui/material';
import { Person as PersonIcon } from '@mui/icons-material';
import ContentLoader from '@/components/UI/ContentLoader';
import CustomTabs from '@/components/UI/CustomTabs';
import DialogBox from '@/components/UI/Modalbox';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import { staticOptions } from '@/helper/common/staticOptions';
import { reportService } from '@/services/reportService';
import { statusClassName } from '@/helper/common/commonFunctions';
import './RotaReportDetails.scss';

const RotaReportDetails = ({ employeeData, onClose, isOpen }) => {
  const [detailsData, setDetailsData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [shiftsData, setShiftsData] = useState([]);
  const [leavesData, setLeavesData] = useState([]);
  const [daysOffData, setDaysOffData] = useState([]);
  const [activeTab, setActiveTab] = useState('shifts');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Sorting states
  const [sortField, setSortField] = useState('');
  const [sortOrder, setSortOrder] = useState('asc');

  // Filter states
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    status: '',
    department: '',
    type: 'all', // all, shifts, swaps, drops
    date_period: 'month',
  });

  // Tab configuration
  const tabs = [
    { id: 'shifts', label: 'Shifts' },
    { id: 'leaves', label: 'Leaves' },
    { id: 'daysoff', label: 'Days Off' },
  ];

  // Column configurations for CommonTable
  const shiftsColumns = [
    {
      header: 'Date',
      accessor: 'date',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'Time',
      accessor: 'startTime',
      sortable: false,
      renderCell: (_, row) => {
        return `${formatTime(row.startTime)} - ${formatTime(row.endTime)}`;
      },
    },
    {
      header: 'Department',
      accessor: 'department',
      sortable: true,
    },
    {
      header: 'Status',
      accessor: 'status',
      sortable: true,
      renderCell: (value) => (
        <span className={statusClassName(value)}>{value}</span>
      ),
    },
    {
      header: 'Hours',
      accessor: 'hours',
      sortable: true,
      renderCell: (value) => `${value || 0} hours`,
    },
    {
      header: 'Swapped With',
      accessor: 'swappedWith',
      sortable: false,
      renderCell: (_, row) => {
        if (row.swappedWith) {
          return (
            <Box display="flex" alignItems="center" gap={1}>
              <Avatar
                src={row.swappedWith.avatar}
                sx={{ width: 24, height: 24 }}
              >
                {row.swappedWith.name?.charAt(0)}
              </Avatar>
              <Typography variant="body2">{row.swappedWith.name}</Typography>
            </Box>
          );
        }
        return 'N/A';
      },
    },
    {
      header: 'Notes',
      accessor: 'notes',
      sortable: false,
      renderCell: (value) => value || 'N/A',
    },
  ];

  const leavesColumns = [
    {
      header: 'Type',
      accessor: 'type',
      sortable: true,
      renderCell: (value) => (
        <span className="annual-text">
          {value === 'annual'
            ? 'Annual Leave'
            : value === 'sick'
              ? 'Sick Leave'
              : value}
        </span>
      ),
    },
    {
      header: 'Start Date',
      accessor: 'startDate',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'End Date',
      accessor: 'endDate',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'Days',
      accessor: 'days',
      sortable: true,
      renderCell: (value) => `${value || 0} day${value !== 1 ? 's' : ''}`,
    },
    {
      header: 'Status',
      accessor: 'status',
      sortable: true,
      renderCell: (value) => (
        <span className={statusClassName(value)}>{value}</span>
      ),
    },
    {
      header: 'Reason',
      accessor: 'reason',
      sortable: false,
    },
    {
      header: 'Applied Date',
      accessor: 'appliedDate',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'Approved By',
      accessor: 'approvedBy',
      sortable: false,
      renderCell: (_, row) => row.approvedBy?.name || 'Pending',
    },
  ];

  const daysOffColumns = [
    {
      header: 'Date',
      accessor: 'date',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'Type',
      accessor: 'type',
      sortable: true,
      renderCell: (value) => (
        <span className="default-d-text">
          {value === 'weekend'
            ? 'Weekend'
            : value === 'holiday'
              ? 'Holiday'
              : value}
        </span>
      ),
    },
    {
      header: 'Status',
      accessor: 'status',
      sortable: true,
      renderCell: (value) => (
        <span className={statusClassName(value)}>{value}</span>
      ),
    },
    {
      header: 'Recurring',
      accessor: 'isRecurring',
      sortable: true,
      renderCell: (value) => (value ? 'Yes' : 'No'),
    },
    {
      header: 'Notes',
      accessor: 'notes',
      sortable: false,
      renderCell: (value) => value || 'N/A',
    },
  ];

  // Filter fields configuration for FilterCollapse
  const filterFields = [
    {
      name: 'type',
      label: 'Type',
      type: 'select',
      options: [
        { value: 'all', label: 'All' },
        { value: 'shifts', label: 'Shifts' },
        { value: 'swaps', label: 'Swaps' },
        { value: 'drops', label: 'Drops' },
      ],
    },
    {
      type: 'select',
      label: 'Shift Status',
      name: 'shift_status',
      options: staticOptions?.SHIFT_STATUS_OPTIONS,
      placeholder: 'Select Shift Status',
    },
    {
      type: 'select',
      label: 'Date',
      name: 'date_period',
      options: staticOptions?.PERIOD_OPTIONS,
      placeholder: 'Select Date',
    },
  ];

  if (filters.date_period === 'custom') {
    filterFields.push({
      type: 'date-range',
      label: 'Date Range',
      name: 'dateRange',
      placeholder: 'Select date range',
      format: 'MMM dd, yyyy',
    });
  }

  useEffect(() => {
    if (isOpen && employeeData) {
      fetchEmployeeDetails();
    }
  }, [isOpen, employeeData]);

  // Refetch data when activeTab changes
  useEffect(() => {
    if (isOpen && employeeData && activeTab) {
      fetchEmployeeDetails();
    }
  }, [activeTab]);

  const fetchEmployeeDetails = async (
    filterParams = null,
    page = currentPage,
    size = rowsPerPage,
    sortBy = sortField,
    sortDirection = sortOrder
  ) => {
    setLoading(true);
    try {
      const employeeId =
        employeeData?.user_id ||
        employeeData?.employment_number ||
        employeeData?.id;

      if (!employeeId) {
        throw new Error('Employee ID not found');
      }

      // Build API parameters
      const apiParams = new URLSearchParams();

      // Add pagination
      apiParams.append('page', page.toString());
      apiParams.append('size', size.toString());

      // Add sorting
      if (sortBy) {
        apiParams.append('sortBy', sortBy);
        apiParams.append('sortDirection', sortDirection);
      }

      // Add filters
      const currentFilters = filterParams || filters;
      if (currentFilters.dateFrom) {
        apiParams.append('dateFrom', currentFilters.dateFrom);
      }
      if (currentFilters.dateTo) {
        apiParams.append('dateTo', currentFilters.dateTo);
      }
      if (currentFilters.status) {
        apiParams.append('status', currentFilters.status);
      }
      if (currentFilters.department) {
        apiParams.append('department', currentFilters.department);
      }
      if (currentFilters.type && currentFilters.type !== 'all') {
        apiParams.append('type', currentFilters.type);
      }
      if (currentFilters.date_period) {
        apiParams.append('date_period', currentFilters.date_period);
      }

      // Add active tab for specific data fetching
      apiParams.append('tab', activeTab);

      const queryString = apiParams.toString();
      const response = await reportService.getEmployeeRotaDetails(
        employeeId,
        queryString ? `?${queryString}` : ''
      );

      if (response?.success && response?.data) {
        setDetailsData(response.data.employee || employeeData);

        // Set data based on active tab or set all data
        if (response.data.shifts) {
          setShiftsData(response.data.shifts);
        }
        if (response.data.leaves) {
          setLeavesData(response.data.leaves);
        }
        if (response.data.daysOff) {
          setDaysOffData(response.data.daysOff);
        }

        // Update total count for pagination
        if (response.data.totalCount) {
          setTotalCount(response.data.totalCount);
        }
      }
    } catch (error) {
      console.error('Error fetching employee details:', error);
      // Fallback to empty data
      setDetailsData(employeeData);
      setShiftsData([]);
      setLeavesData([]);
      setDaysOffData([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    setCurrentPage(1); // Reset pagination when changing tabs
    fetchEmployeeDetails(); // Refetch data for new tab
  };

  // Pagination handlers
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
    fetchEmployeeDetails(null, newPage, rowsPerPage, sortField, sortOrder);
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1);
    fetchEmployeeDetails(null, 1, newRowsPerPage, sortField, sortOrder);
  };

  // Sorting handler
  const handleSort = (field, direction) => {
    setSortField(field);
    setSortOrder(direction);
    setCurrentPage(1);
    fetchEmployeeDetails(null, 1, rowsPerPage, field, direction);
  };

  // Filter handlers
  const handleApplyFilters = (newFilters) => {
    setFilters(newFilters);
    setCurrentPage(1);
    fetchEmployeeDetails(newFilters, 1, rowsPerPage, sortField, sortOrder);
  };

  const handleFieldChange = (fieldName, value) => {
    const updatedFilters = { ...filters, [fieldName]: value };
    setFilters(updatedFilters);
    // Optionally trigger immediate filtering for certain fields
    if (fieldName === 'status' || fieldName === 'department') {
      setCurrentPage(1);
      fetchEmployeeDetails(
        updatedFilters,
        1,
        rowsPerPage,
        sortField,
        sortOrder
      );
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-GB', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatTime = (timeString) => {
    if (!timeString) return 'N/A';
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Render functions for each tab
  const renderShiftsTab = () => (
    <Box>
      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        initialValues={filters}
        onFieldChange={handleFieldChange}
      />

      <Box className="shifts-table-container" mt={2}>
        <CommonTable
          columns={shiftsColumns}
          data={shiftsData}
          loading={loading}
          currentPage={currentPage}
          totalCount={totalCount}
          pageSize={rowsPerPage}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          onSort={handleSort}
          sortOrder={{ field: sortField, direction: sortOrder }}
          showPagination={true}
        />
      </Box>
    </Box>
  );

  const renderLeavesTab = () => (
    <Box className="leaves-table-container">
      <CommonTable
        columns={leavesColumns}
        data={leavesData}
        loading={loading}
        currentPage={currentPage}
        totalCount={totalCount}
        pageSize={rowsPerPage}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        onSort={handleSort}
        sortOrder={{ field: sortField, direction: sortOrder }}
        showPagination={true}
      />
    </Box>
  );

  const renderDaysOffTab = () => (
    <Box className="daysoff-table-container">
      <CommonTable
        columns={daysOffColumns}
        data={daysOffData}
        loading={loading}
        currentPage={currentPage}
        totalCount={totalCount}
        pageSize={rowsPerPage}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        onSort={handleSort}
        sortOrder={{ field: sortField, direction: sortOrder }}
        showPagination={true}
      />
    </Box>
  );

  const getCurrentTabContent = () => {
    switch (activeTab) {
      case 'shifts':
        return renderShiftsTab();
      case 'leaves':
        return renderLeavesTab();
      case 'daysoff':
        return renderDaysOffTab();
      default:
        return renderShiftsTab();
    }
  };

  // Handle case when no employee data is available
  if (!isOpen || !employeeData) {
    return null;
  }

  // Create the title with employee info
  const dialogTitle = (
    <Box className="employee-header-info">
      <Avatar className="employee-avatar" src={detailsData?.avatar}>
        {detailsData?.name?.charAt(0) || <PersonIcon />}
      </Avatar>
      <Box className="employee-details">
        <Typography variant="h6" className="employee-name">
          {detailsData?.name || employeeData?.user_full_name || 'Employee'}
        </Typography>
        <Typography variant="body2" className="employee-id">
          ID:{' '}
          {detailsData?.employeeId || employeeData?.employment_number || 'N/A'}
        </Typography>
        {(detailsData?.role || detailsData?.department) && (
          <Typography variant="body2" className="employee-role">
            {detailsData?.role} • {detailsData?.department}
          </Typography>
        )}
      </Box>
    </Box>
  );

  // Create the content for the dialog
  const dialogContent = (
    <Box className="rota-details-content">
      {loading ? (
        <ContentLoader />
      ) : (
        <>
          {/* Employee Summary */}
          <Card className="summary-card">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Summary
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box className="summary-item">
                    <Typography variant="body2" color="textSecondary">
                      Total Shifts
                    </Typography>
                    <Typography variant="h6" color="primary">
                      {detailsData?.totalShifts || shiftsData?.length || 0}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box className="summary-item">
                    <Typography variant="body2" color="textSecondary">
                      Total Hours
                    </Typography>
                    <Typography variant="h6" color="primary">
                      {detailsData?.totalHours ||
                        shiftsData?.reduce(
                          (total, shift) => total + (shift.hours || 0),
                          0
                        ) ||
                        0}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box className="summary-item">
                    <Typography variant="body2" color="textSecondary">
                      Leave Days
                    </Typography>
                    <Typography variant="h6" color="warning">
                      {detailsData?.leaveDays ||
                        leavesData?.reduce(
                          (total, leave) => total + (leave.days || 0),
                          0
                        ) ||
                        0}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box className="summary-item">
                    <Typography variant="body2" color="textSecondary">
                      Days Off
                    </Typography>
                    <Typography variant="h6" color="info">
                      {detailsData?.daysOff || daysOffData?.length || 0}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Divider sx={{ my: 3 }} />

          {/* Tabs Section */}
          <Box className="details-tabs-section">
            <CustomTabs
              tabs={tabs}
              initialTab={activeTab}
              onTabChange={handleTabChange}
            />
            <Box className="tab-content" sx={{ mt: 2 }}>
              {getCurrentTabContent()}
            </Box>
          </Box>
        </>
      )}
    </Box>
  );

  return (
    <DialogBox
      open={isOpen}
      handleClose={onClose}
      title={dialogTitle}
      content={dialogContent}
      className="fullscreen-dialog-box-container rota-details-dialog"
      onCloseStatus={true}
    />
  );
};

export default RotaReportDetails;
